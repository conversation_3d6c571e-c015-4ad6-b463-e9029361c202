/**
 * Comprehensive Model Information Database
 * 
 * Contains detailed metadata for AI models including context lengths,
 * capabilities, and provider-specific information.
 */

export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  contextLength: number;
  maxOutputTokens?: number;
  supportsImages: boolean;
  supportsTools: boolean;
  supportsStreaming: boolean;
  inputCostPer1kTokens?: number;
  outputCostPer1kTokens?: number;
  description?: string;
  releaseDate?: string;
  deprecated?: boolean;
  aliases?: string[];
}

/**
 * Comprehensive model database with latest models
 */
export const MODEL_DATABASE: Record<string, ModelInfo> = {
  // OpenAI Models
  'gpt-4': {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: 'openai',
    contextLength: 8192,
    maxOutputTokens: 4096,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    inputCostPer1kTokens: 0.03,
    outputCostPer1kTokens: 0.06,
    description: 'Most capable GPT-4 model, great for complex tasks',
    releaseDate: '2023-03-14',
  },

  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'openai',
    contextLength: 128000,
    maxOutputTokens: 4096,
    supportsImages: true,
    supportsTools: true,
    supportsStreaming: true,
    inputCostPer1kTokens: 0.01,
    outputCostPer1kTokens: 0.03,
    description: 'Latest GPT-4 model with 128k context and vision',
    releaseDate: '2024-04-09',
  },

  'gpt-4-turbo-preview': {
    id: 'gpt-4-turbo-preview',
    name: 'GPT-4 Turbo Preview',
    provider: 'openai',
    contextLength: 128000,
    maxOutputTokens: 4096,
    supportsImages: true,
    supportsTools: true,
    supportsStreaming: true,
    inputCostPer1kTokens: 0.01,
    outputCostPer1kTokens: 0.03,
    description: 'Preview version of GPT-4 Turbo',
    releaseDate: '2023-11-06',
  },

  'gpt-4-vision-preview': {
    id: 'gpt-4-vision-preview',
    name: 'GPT-4 Vision Preview',
    provider: 'openai',
    contextLength: 128000,
    maxOutputTokens: 4096,
    supportsImages: true,
    supportsTools: true,
    supportsStreaming: true,
    inputCostPer1kTokens: 0.01,
    outputCostPer1kTokens: 0.03,
    description: 'GPT-4 with vision capabilities',
    releaseDate: '2023-09-25',
  },

  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'openai',
    contextLength: 4096,
    maxOutputTokens: 4096,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    inputCostPer1kTokens: 0.0015,
    outputCostPer1kTokens: 0.002,
    description: 'Fast and efficient model for most tasks',
    releaseDate: '2023-03-01',
  },

  'gpt-3.5-turbo-16k': {
    id: 'gpt-3.5-turbo-16k',
    name: 'GPT-3.5 Turbo 16K',
    provider: 'openai',
    contextLength: 16384,
    maxOutputTokens: 4096,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    inputCostPer1kTokens: 0.003,
    outputCostPer1kTokens: 0.004,
    description: 'GPT-3.5 with extended context length',
    releaseDate: '2023-06-13',
  },

  'o1-preview': {
    id: 'o1-preview',
    name: 'o1 Preview',
    provider: 'openai',
    contextLength: 128000,
    maxOutputTokens: 32768,
    supportsImages: false,
    supportsTools: false,
    supportsStreaming: false,
    inputCostPer1kTokens: 0.015,
    outputCostPer1kTokens: 0.06,
    description: 'Advanced reasoning model with enhanced problem-solving',
    releaseDate: '2024-09-12',
  },

  'o1-mini': {
    id: 'o1-mini',
    name: 'o1 Mini',
    provider: 'openai',
    contextLength: 128000,
    maxOutputTokens: 65536,
    supportsImages: false,
    supportsTools: false,
    supportsStreaming: false,
    inputCostPer1kTokens: 0.003,
    outputCostPer1kTokens: 0.012,
    description: 'Faster and cheaper reasoning model',
    releaseDate: '2024-09-12',
  },

  'o3-mini': {
    id: 'o3-mini',
    name: 'o3 Mini',
    provider: 'openai',
    contextLength: 128000,
    maxOutputTokens: 65536,
    supportsImages: false,
    supportsTools: false,
    supportsStreaming: false,
    description: 'Latest reasoning model with improved capabilities',
    releaseDate: '2024-12-20',
  },

  // Google Gemini Models
  'gemini-pro': {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    provider: 'gemini',
    contextLength: 32768,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Google\'s most capable model for text tasks',
    releaseDate: '2023-12-06',
  },

  'gemini-pro-vision': {
    id: 'gemini-pro-vision',
    name: 'Gemini Pro Vision',
    provider: 'gemini',
    contextLength: 32768,
    supportsImages: true,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Gemini Pro with vision capabilities',
    releaseDate: '2023-12-06',
  },

  'gemini-1.5-pro': {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    provider: 'gemini',
    contextLength: 1000000,
    supportsImages: true,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Latest Gemini model with 1M token context',
    releaseDate: '2024-02-15',
  },

  'gemini-1.5-flash': {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    provider: 'gemini',
    contextLength: 1000000,
    supportsImages: true,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Fast and efficient Gemini model',
    releaseDate: '2024-05-14',
  },

  // Mistral Models
  'mistral-large-latest': {
    id: 'mistral-large-latest',
    name: 'Mistral Large',
    provider: 'mistral',
    contextLength: 32768,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Mistral\'s most capable model',
    releaseDate: '2024-02-26',
  },

  'mixtral-8x7b-instruct': {
    id: 'mixtral-8x7b-instruct',
    name: 'Mixtral 8x7B Instruct',
    provider: 'mistral',
    contextLength: 32768,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Mixture of experts model with strong performance',
    releaseDate: '2023-12-11',
  },

  'mixtral-8x22b-instruct': {
    id: 'mixtral-8x22b-instruct',
    name: 'Mixtral 8x22B Instruct',
    provider: 'mistral',
    contextLength: 65536,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Larger mixture of experts model',
    releaseDate: '2024-04-17',
  },

  // xAI Models
  'grok-beta': {
    id: 'grok-beta',
    name: 'Grok Beta',
    provider: 'xai',
    contextLength: 131072,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    description: 'xAI\'s conversational AI model',
    releaseDate: '2023-11-04',
  },

  'grok-vision-beta': {
    id: 'grok-vision-beta',
    name: 'Grok Vision Beta',
    provider: 'xai',
    contextLength: 131072,
    supportsImages: true,
    supportsTools: true,
    supportsStreaming: true,
    description: 'Grok with vision capabilities',
    releaseDate: '2024-04-12',
  },

  // DeepSeek Models
  'deepseek-chat': {
    id: 'deepseek-chat',
    name: 'DeepSeek Chat',
    provider: 'deepseek',
    contextLength: 65536,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    description: 'DeepSeek-V3-0324 - Advanced general purpose chat model',
    releaseDate: '2025-03-24',
  },

  'deepseek-reasoner': {
    id: 'deepseek-reasoner',
    name: 'DeepSeek Reasoner',
    provider: 'deepseek',
    contextLength: 65536,
    supportsImages: false,
    supportsTools: true,
    supportsStreaming: true,
    description: 'DeepSeek-R1-0528 - Advanced reasoning model with chain-of-thought',
    releaseDate: '2025-05-28',
  },
};

/**
 * Get model information by ID
 */
export function getModelInfo(modelId: string): ModelInfo | undefined {
  return MODEL_DATABASE[modelId];
}

/**
 * Get all models for a provider
 */
export function getModelsForProvider(provider: string): ModelInfo[] {
  return Object.values(MODEL_DATABASE).filter(model => model.provider === provider);
}

/**
 * Search models by name or description
 */
export function searchModels(query: string): ModelInfo[] {
  const lowerQuery = query.toLowerCase();
  return Object.values(MODEL_DATABASE).filter(model =>
    model.name.toLowerCase().includes(lowerQuery) ||
    model.id.toLowerCase().includes(lowerQuery) ||
    model.description?.toLowerCase().includes(lowerQuery) ||
    model.aliases?.some(alias => alias.toLowerCase().includes(lowerQuery)),
  );
}

/**
 * Get models with specific capabilities
 */
export function getModelsWithCapabilities(capabilities: {
  supportsImages?: boolean;
  supportsTools?: boolean;
  supportsStreaming?: boolean;
  minContextLength?: number;
}): ModelInfo[] {
  return Object.values(MODEL_DATABASE).filter(model => {
    if (capabilities.supportsImages !== undefined && model.supportsImages !== capabilities.supportsImages) {
      return false;
    }
    if (capabilities.supportsTools !== undefined && model.supportsTools !== capabilities.supportsTools) {
      return false;
    }
    if (capabilities.supportsStreaming !== undefined && model.supportsStreaming !== capabilities.supportsStreaming) {
      return false;
    }
    if (capabilities.minContextLength !== undefined && model.contextLength < capabilities.minContextLength) {
      return false;
    }
    return true;
  });
}

/**
 * Get recommended models for coding tasks
 */
export function getCodingModels(): ModelInfo[] {
  const codingModelIds = [
    'gpt-4',
    'gpt-4-turbo',
    'o1-preview',
    'o1-mini',
    'deepseek-chat',
    'deepseek-reasoner',
    'gemini-1.5-pro',
    'mistral-large-latest',
  ];

  return codingModelIds
    .map(id => MODEL_DATABASE[id])
    .filter(Boolean);
}

/**
 * Get models sorted by context length
 */
export function getModelsByContextLength(descending = true): ModelInfo[] {
  const models = Object.values(MODEL_DATABASE);
  return models.sort((a, b) => 
    descending ? b.contextLength - a.contextLength : a.contextLength - b.contextLength,
  );
}

/**
 * Check if model is deprecated
 */
export function isModelDeprecated(modelId: string): boolean {
  const model = MODEL_DATABASE[modelId];
  return model?.deprecated || false;
}

/**
 * Get model aliases
 */
export function getModelAliases(modelId: string): string[] {
  const model = MODEL_DATABASE[modelId];
  return model?.aliases || [];
}

/**
 * Resolve model alias to actual model ID
 */
export function resolveModelAlias(alias: string): string {
  for (const [modelId, model] of Object.entries(MODEL_DATABASE)) {
    if (model.aliases?.includes(alias)) {
      return modelId;
    }
  }
  return alias; // Return original if no alias found
}
