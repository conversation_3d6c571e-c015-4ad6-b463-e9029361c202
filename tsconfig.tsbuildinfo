{"root": ["./src/app.tsx", "./src/approvals.ts", "./src/cli-singlepass.tsx", "./src/cli.tsx", "./src/format-command.ts", "./src/text-buffer.ts", "./src/version.ts", "./src/components/chat/multiline-editor.tsx", "./src/components/chat/terminal-chat-command-review.tsx", "./src/components/chat/terminal-chat-input-thinking.tsx", "./src/components/chat/terminal-chat-input.tsx", "./src/components/chat/terminal-chat-response-item.tsx", "./src/components/chat/terminal-chat.tsx", "./src/components/overlays/approval-mode-overlay.tsx", "./src/components/overlays/diff-overlay.tsx", "./src/components/overlays/help-overlay.tsx", "./src/components/overlays/history-overlay.tsx", "./src/components/overlays/index.ts", "./src/components/overlays/model-overlay.tsx", "./src/components/overlays/sessions-overlay.tsx", "./src/components/select-input/indicator.tsx", "./src/components/select-input/select-input.tsx", "./src/components/vendor/progress-bar.tsx", "./src/components/vendor/spinner.tsx", "./src/components/vendor/text-input.tsx", "./src/components/vendor/components/box.tsx", "./src/components/vendor/components/index.ts", "./src/components/vendor/components/list.tsx", "./src/components/vendor/components/screen.tsx", "./src/hooks/use-confirmation.ts", "./src/hooks/use-state.ts", "./src/hooks/use-terminal-size.ts", "./src/types/blessed-extensions.ts", "./src/types/index.ts", "./src/utils/bug-report.ts", "./src/utils/check-in-git.ts", "./src/utils/check-updates.ts", "./src/utils/config-wizard.tsx", "./src/utils/config.ts", "./src/utils/debug-validation.ts", "./src/utils/error-utils.ts", "./src/utils/file-tag-utils.ts", "./src/utils/get-api-key.tsx", "./src/utils/get-diff.ts", "./src/utils/input-utils.ts", "./src/utils/model-info.ts", "./src/utils/model-utils.ts", "./src/utils/notifications.ts", "./src/utils/onboarding.tsx", "./src/utils/openai-client.ts", "./src/utils/package-manager-detector.ts", "./src/utils/providers.ts", "./src/utils/responses.ts", "./src/utils/slash-commands.ts", "./src/utils/terminal.ts", "./src/utils/agent/agent-loop.ts", "./src/utils/agent/apply-patch.ts", "./src/utils/agent/handle-exec-command.ts", "./src/utils/agent/platform-commands.ts", "./src/utils/agent/sandbox/index.ts", "./src/utils/agent/sandbox/unix-sandbox.ts", "./src/utils/agent/sandbox/windows-sandbox.ts", "./src/utils/logger/log.ts", "./src/utils/singlepass/context-limit.ts", "./src/utils/singlepass/file-ops.ts", "./src/utils/storage/command-history.ts", "./src/utils/storage/save-rollout.ts"], "version": "5.8.3"}