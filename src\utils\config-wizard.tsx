/**
 * Interactive Configuration Wizard
 * 
 * Provides a comprehensive interface for configuring providers,
 * API keys, models, and preferences with validation and testing.
 */

import chalk from 'chalk';
import { loadConfig, saveConfig, getApiKey, clearConfigCache } from './config.js';
import { getProviderNames, getProvider } from './providers.js';
import { validateApiKey } from './get-api-key.js';
import { logError } from './logger/log.js';
import type { AppConfig, ProviderConfig, ApprovalPolicy } from '../types/index.js';

export interface WizardOptions {
  provider?: string;
  skipApiKey?: boolean;
  skipValidation?: boolean;
  global?: boolean;
}

export interface WizardResult {
  success: boolean;
  config: AppConfig;
  error?: string;
}

/**
 * Main configuration wizard
 */
export class ConfigurationWizard {
  private config: AppConfig;
  private options: WizardOptions;

  constructor(options: WizardOptions = {}) {
    this.config = loadConfig();
    this.options = options;
  }

  /**
   * Run the complete configuration wizard
   */
  async run(): Promise<WizardResult> {
    try {
      console.clear();
      this.displayHeader();

      // Step 1: Provider selection
      if (!this.options.provider) {
        await this.selectProvider();
      } else {
        this.config.provider = this.options.provider;
      }

      // Step 2: API key configuration
      if (!this.options.skipApiKey && this.config.provider !== 'ollama') {
        await this.configureApiKey();
      }

      // Step 3: Model selection
      await this.selectModel();

      // Step 4: Preferences configuration
      await this.configurePreferences();

      // Step 5: Test configuration
      await this.testConfiguration();

      // Step 6: Save configuration
      await this.saveConfiguration();

      this.displaySuccess();

      return {
        success: true,
        config: this.config,
      };

    } catch (error) {
      logError('Configuration wizard failed', error as Error);
      return {
        success: false,
        config: this.config,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Display wizard header
   */
  private displayHeader(): void {
    console.log(chalk.cyan('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.cyan('║                Configuration Wizard                         ║'));
    console.log(chalk.cyan('║              Kritrima AI CLI Setup                          ║'));
    console.log(chalk.cyan('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
    console.log(chalk.blue('This wizard will guide you through configuring your AI assistant.'));
    console.log('');
  }

  /**
   * Provider selection step
   */
  private async selectProvider(): Promise<void> {
    const { default: inquirer } = await import('inquirer');
    
    console.log(chalk.cyan('Step 1: Choose AI Provider'));
    console.log('');

    const providers = getProviderNames();
    const choices = providers.map(providerName => {
      const provider = getProvider(providerName);
      const hasApiKey = !!getApiKey(providerName);
      const status = hasApiKey ? chalk.green('✓') : chalk.gray('○');
      
      return {
        name: `${status} ${provider?.name || providerName} - ${provider?.models?.[0] || 'Various models'}`,
        value: providerName,
        short: provider?.name || providerName,
      };
    });

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select your AI provider:',
        choices,
        pageSize: 12,
      },
    ]);

    this.config.provider = provider;
    console.log(chalk.green(`✓ Selected: ${getProvider(provider)?.name}`));
    console.log('');
  }

  /**
   * API key configuration step
   */
  private async configureApiKey(): Promise<void> {
    const { default: inquirer } = await import('inquirer');
    const providerConfig = getProvider(this.config.provider);
    
    if (!providerConfig) {
      throw new Error(`Unknown provider: ${this.config.provider}`);
    }

    console.log(chalk.cyan('Step 2: Configure API Key'));
    console.log('');

    // Check if API key already exists
    const existingKey = getApiKey(this.config.provider);
    if (existingKey) {
      const { useExisting } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'useExisting',
          message: `API key already configured for ${providerConfig.name}. Use existing key?`,
          default: true,
        },
      ]);

      if (useExisting) {
        console.log(chalk.green('✓ Using existing API key'));
        console.log('');
        return;
      }
    }

    // Display instructions
    this.displayApiKeyInstructions(this.config.provider, providerConfig);

    // Get new API key
    const { apiKey } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter your ${providerConfig.name} API key:`,
        mask: '*',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'API key is required';
          }
          if (input.length < 10) {
            return 'API key seems too short';
          }
          return true;
        },
      },
    ]);

    // Validate API key
    if (!this.options.skipValidation) {
      console.log(chalk.blue('Validating API key...'));
      
      // Set environment variable temporarily
      const envKey = providerConfig.envKey;
      const originalValue = process.env[envKey];
      process.env[envKey] = apiKey.trim();

      try {
        const isValid = await validateApiKey(this.config.provider, apiKey.trim());
        
        if (isValid) {
          console.log(chalk.green('✓ API key is valid'));
        } else {
          // Restore original value
          if (originalValue !== undefined) {
            process.env[envKey] = originalValue;
          } else {
            delete process.env[envKey];
          }
          throw new Error('API key validation failed');
        }
      } catch (error) {
        // Restore original value
        if (originalValue !== undefined) {
          process.env[envKey] = originalValue;
        } else {
          delete process.env[envKey];
        }
        throw error;
      }
    } else {
      // Just set the environment variable
      const envKey = providerConfig.envKey;
      process.env[envKey] = apiKey.trim();
    }

    console.log('');
  }

  /**
   * Display API key instructions
   */
  private displayApiKeyInstructions(provider: string, config: ProviderConfig): void {
    const instructions: Record<string, string[]> = {
      openai: [
        '1. Visit: https://platform.openai.com/account/api-keys',
        '2. Click "Create new secret key"',
        '3. Copy the key (starts with sk-)',
      ],
      azure: [
        '1. Go to Azure Portal → Your OpenAI resource',
        '2. Navigate to "Keys and Endpoint"',
        '3. Copy one of the API keys',
      ],
      gemini: [
        '1. Visit: https://makersuite.google.com/app/apikey',
        '2. Click "Create API key"',
        '3. Copy the generated key',
      ],
      mistral: [
        '1. Visit: https://console.mistral.ai/api-keys/',
        '2. Click "Create new key"',
        '3. Copy the generated key',
      ],
      deepseek: [
        '1. Visit: https://platform.deepseek.com/api_keys',
        '2. Click "Create API Key"',
        '3. Copy the generated key',
      ],
      xai: [
        '1. Visit: https://console.x.ai/api-keys',
        '2. Click "Create API Key"',
        '3. Copy the generated key',
      ],
      groq: [
        '1. Visit: https://console.groq.com/keys',
        '2. Click "Create API Key"',
        '3. Copy the generated key',
      ],
    };

    const providerInstructions = instructions[provider.toLowerCase()];
    if (providerInstructions) {
      console.log(chalk.blue(`To get your ${config.name} API key:`));
      providerInstructions.forEach(step => {
        console.log(chalk.gray(`  ${step}`));
      });
      console.log('');
    }
  }

  /**
   * Model selection step
   */
  private async selectModel(): Promise<void> {
    const { default: inquirer } = await import('inquirer');
    
    console.log(chalk.cyan('Step 3: Choose Model'));
    console.log('');

    const providerConfig = getProvider(this.config.provider);
    if (!providerConfig || !providerConfig.models || providerConfig.models.length === 0) {
      this.config.model = providerConfig?.defaultModel || 'gpt-4';
      console.log(chalk.green(`✓ Using default model: ${this.config.model}`));
      console.log('');
      return;
    }

    const choices = providerConfig.models.map(model => ({
      name: model,
      value: model,
    }));

    const { model } = await inquirer.prompt([
      {
        type: 'list',
        name: 'model',
        message: `Choose a model for ${providerConfig.name}:`,
        choices,
        default: providerConfig.defaultModel,
        pageSize: 10,
      },
    ]);

    this.config.model = model;
    console.log(chalk.green(`✓ Selected model: ${model}`));
    console.log('');
  }

  /**
   * Preferences configuration step
   */
  private async configurePreferences(): Promise<void> {
    const { default: inquirer } = await import('inquirer');
    
    console.log(chalk.cyan('Step 4: Configure Preferences'));
    console.log('');

    const preferences = await inquirer.prompt([
      {
        type: 'list',
        name: 'approvalMode',
        message: 'Choose approval mode:',
        choices: [
          {
            name: 'Suggest - Manual approval for all actions (Recommended for beginners)',
            value: 'suggest',
          },
          {
            name: 'Auto-edit - Automatic file edits, manual command approval',
            value: 'auto-edit',
          },
          {
            name: 'Full-auto - Automatic everything (Use with caution)',
            value: 'full-auto',
          },
        ],
        default: this.config.approvalMode || 'suggest',
      },
      {
        type: 'confirm',
        name: 'notifications',
        message: 'Enable desktop notifications?',
        default: this.config.notifications !== false,
      },
      {
        type: 'confirm',
        name: 'saveHistory',
        message: 'Save command history?',
        default: this.config.saveHistory !== false,
      },
      {
        type: 'confirm',
        name: 'debug',
        message: 'Enable debug logging?',
        default: this.config.debug || false,
      },
    ]);

    // Apply preferences
    this.config.approvalMode = preferences.approvalMode as ApprovalPolicy;
    this.config.notifications = preferences.notifications;
    this.config.saveHistory = preferences.saveHistory;
    this.config.debug = preferences.debug;

    console.log(chalk.green('✓ Preferences configured'));
    console.log('');
  }

  /**
   * Test configuration step
   */
  private async testConfiguration(): Promise<void> {
    console.log(chalk.cyan('Step 5: Test Configuration'));
    console.log('');

    if (this.config.provider === 'ollama') {
      console.log(chalk.yellow('⚠️  Skipping test for Ollama (local provider)'));
      console.log('');
      return;
    }

    try {
      console.log(chalk.blue('Testing API connection...'));
      
      const isValid = await validateApiKey(this.config.provider);
      
      if (isValid) {
        console.log(chalk.green('✅ Configuration test passed!'));
      } else {
        throw new Error('Configuration test failed');
      }
    } catch (error) {
      console.log(chalk.red('❌ Configuration test failed'));
      throw error;
    }

    console.log('');
  }

  /**
   * Save configuration step
   */
  private async saveConfiguration(): Promise<void> {
    console.log(chalk.cyan('Step 6: Save Configuration'));
    console.log('');

    // Ensure required fields are set
    if (!this.config.maxTokens) {
      this.config.maxTokens = 4096;
    }
    if (!this.config.temperature) {
      this.config.temperature = 0.7;
    }
    if (!this.config.timeout) {
      this.config.timeout = 30000;
    }
    if (!this.config.maxIterations) {
      this.config.maxIterations = 10;
    }
    if (!this.config.workingDirectory) {
      this.config.workingDirectory = process.cwd();
    }
    if (!this.config.additionalWritableRoots) {
      this.config.additionalWritableRoots = [];
    }
    if (this.config.disableResponseStorage === undefined) {
      this.config.disableResponseStorage = false;
    }

    // Save configuration
    saveConfig(this.config, this.options.global);
    clearConfigCache();

    console.log(chalk.green('✓ Configuration saved'));
    console.log('');
  }

  /**
   * Display success message
   */
  private displaySuccess(): void {
    console.log(chalk.green('🎉 Configuration completed successfully!'));
    console.log('');
    console.log(chalk.blue('Your configuration:'));
    console.log(chalk.blue(`  Provider: ${this.config.provider}`));
    console.log(chalk.blue(`  Model: ${this.config.model}`));
    console.log(chalk.blue(`  Approval Mode: ${this.config.approvalMode}`));
    console.log('');
  }
}

/**
 * Run configuration wizard
 */
export async function runConfigurationWizard(options: WizardOptions = {}): Promise<WizardResult> {
  const wizard = new ConfigurationWizard(options);
  return wizard.run();
}
