/**
 * Configuration Management System
 *
 * Handles multi-format configuration loading, environment variables,
 * and configuration hierarchy management.
 */
import { readFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { parse as parseYaml } from 'yaml';
import { providers, getProvider } from './providers.js';
// Default configuration values
const DEFAULT_CONFIG = {
    model: 'gpt-4',
    provider: 'openai',
    approvalMode: 'auto',
    maxTokens: 4096,
    temperature: 0.7,
    timeout: 30000,
    debug: false,
    notifications: true,
    saveHistory: true,
    maxHistorySize: 1000,
    maxIterations: 10,
    workingDirectory: process.cwd(),
    additionalWritableRoots: [],
    disableResponseStorage: false,
    providers: providers, // Include all available providers
};
// Configuration file paths
function getConfigPaths() {
    const isTest = process.env.NODE_ENV === 'test' || process.env.KRITRIMA_TEST_MODE === 'true';
    const configDir = isTest && process.env.KRITRIMA_CONFIG_DIR
        ? process.env.KRITRIMA_CONFIG_DIR
        : join(homedir(), '.kritrima-ai');
    return {
        global: join(configDir, 'config.json'),
        globalYaml: join(configDir, 'config.yaml'),
        project: join(process.cwd(), '.kritrima-ai', 'config.json'),
        projectYaml: join(process.cwd(), '.kritrima-ai', 'config.yaml'),
    };
}
let cachedConfig = null;
/**
 * Load configuration from multiple sources with proper hierarchy
 */
export function loadConfig() {
    if (cachedConfig) {
        return cachedConfig;
    }
    let config = { ...DEFAULT_CONFIG };
    const configPaths = getConfigPaths();
    // 1. Load global configuration
    const globalConfig = loadConfigFile(configPaths.global) ||
        loadConfigFile(configPaths.globalYaml);
    if (globalConfig) {
        config = mergeConfigs(config, globalConfig);
    }
    // 2. Load project configuration
    const projectConfig = loadConfigFile(configPaths.project) ||
        loadConfigFile(configPaths.projectYaml);
    if (projectConfig) {
        config = mergeConfigs(config, projectConfig);
    }
    // 3. Apply environment variables
    config = applyEnvironmentVariables(config);
    // 4. Discover project documentation
    const projectDocPath = discoverProjectDocPath(config.workingDirectory || process.cwd());
    if (projectDocPath) {
        config.projectDocPath = projectDocPath;
    }
    // Cache and return
    cachedConfig = config;
    return config;
}
/**
 * Load configuration from a specific file
 */
function loadConfigFile(filePath) {
    if (!existsSync(filePath)) {
        return null;
    }
    try {
        const content = readFileSync(filePath, 'utf-8');
        if (filePath.endsWith('.yaml') || filePath.endsWith('.yml')) {
            return parseYaml(content);
        }
        else {
            return JSON.parse(content);
        }
    }
    catch (error) {
        console.warn(`Warning: Could not parse config file ${filePath}:`, error);
        return null;
    }
}
/**
 * Merge two configuration objects with deep merging
 */
function mergeConfigs(base, override) {
    const merged = { ...base };
    for (const [key, value] of Object.entries(override)) {
        if (value !== undefined) {
            if (key === 'providers' && typeof value === 'object' && value !== null && !Array.isArray(value)) {
                merged.providers = { ...merged.providers, ...value };
            }
            else if (key === 'additionalWritableRoots' && Array.isArray(value)) {
                merged.additionalWritableRoots = [...(merged.additionalWritableRoots || []), ...value];
            }
            else {
                merged[key] = value;
            }
        }
    }
    return merged;
}
/**
 * Apply environment variables to configuration
 */
function applyEnvironmentVariables(config) {
    const envConfig = {};
    // Model and provider from environment
    if (process.env.KRITRIMA_MODEL) {
        envConfig.model = process.env.KRITRIMA_MODEL;
    }
    if (process.env.KRITRIMA_PROVIDER) {
        envConfig.provider = process.env.KRITRIMA_PROVIDER;
    }
    if (process.env.KRITRIMA_APPROVAL_MODE) {
        envConfig.approvalMode = process.env.KRITRIMA_APPROVAL_MODE;
    }
    // Numeric settings
    if (process.env.KRITRIMA_MAX_TOKENS) {
        envConfig.maxTokens = parseInt(process.env.KRITRIMA_MAX_TOKENS, 10);
    }
    if (process.env.KRITRIMA_TEMPERATURE) {
        envConfig.temperature = parseFloat(process.env.KRITRIMA_TEMPERATURE);
    }
    if (process.env.KRITRIMA_TIMEOUT) {
        envConfig.timeout = parseInt(process.env.KRITRIMA_TIMEOUT, 10);
    }
    if (process.env.KRITRIMA_MAX_ITERATIONS) {
        envConfig.maxIterations = parseInt(process.env.KRITRIMA_MAX_ITERATIONS, 10);
    }
    // Boolean settings
    if (process.env.KRITRIMA_DEBUG) {
        envConfig.debug = process.env.KRITRIMA_DEBUG === 'true';
    }
    if (process.env.KRITRIMA_NOTIFICATIONS) {
        envConfig.notifications = process.env.KRITRIMA_NOTIFICATIONS === 'true';
    }
    if (process.env.KRITRIMA_SAVE_HISTORY) {
        envConfig.saveHistory = process.env.KRITRIMA_SAVE_HISTORY === 'true';
    }
    // Working directory
    if (process.env.KRITRIMA_WORKDIR) {
        envConfig.workingDirectory = process.env.KRITRIMA_WORKDIR;
    }
    return mergeConfigs(config, envConfig);
}
/**
 * Discover project documentation path
 */
export function discoverProjectDocPath(startDir) {
    const candidates = [
        'AGENTS.md',
        'README.md',
        'docs/README.md',
        'docs/AGENTS.md',
        '.kritrima-ai/README.md',
        '.kritrima-ai/AGENTS.md',
    ];
    let currentDir = startDir;
    const maxDepth = 5;
    let depth = 0;
    while (depth < maxDepth) {
        for (const candidate of candidates) {
            const candidatePath = join(currentDir, candidate);
            if (existsSync(candidatePath)) {
                return candidatePath;
            }
        }
        const parentDir = dirname(currentDir);
        if (parentDir === currentDir) {
            break; // Reached root directory
        }
        currentDir = parentDir;
        depth++;
    }
    return null;
}
/**
 * Save configuration to file
 */
export function saveConfig(config, global = false) {
    const configPaths = getConfigPaths();
    const configPath = global ? configPaths.global : configPaths.project;
    const configDir = dirname(configPath);
    // Ensure directory exists
    if (!existsSync(configDir)) {
        mkdirSync(configDir, { recursive: true });
    }
    // Write configuration
    writeFileSync(configPath, JSON.stringify(config, null, 2));
    // Clear cache to force reload
    cachedConfig = null;
}
/**
 * Get API key for a provider
 */
export function getApiKey(provider = 'openai') {
    const config = loadConfig();
    const providerInfo = config.providers?.[provider.toLowerCase()];
    if (providerInfo) {
        return process.env[providerInfo.envKey];
    }
    // Fallback to standard environment variable patterns
    const envKeys = [
        `${provider.toUpperCase()}_API_KEY`,
        'OPENAI_API_KEY', // Default fallback
    ];
    for (const key of envKeys) {
        const value = process.env[key];
        if (value) {
            return value;
        }
    }
    return undefined;
}
/**
 * Get base URL for a provider
 */
export function getBaseUrl(provider = 'openai') {
    const config = loadConfig();
    // First check if we have provider info in the config
    const providerInfo = config.providers?.[provider.toLowerCase()];
    if (providerInfo) {
        // Check for environment override
        const envKey = `${provider.toUpperCase()}_BASE_URL`;
        return process.env[envKey] || providerInfo.baseURL;
    }
    // If no provider info in config, check the static providers list
    const staticProviderInfo = getProvider(provider);
    if (staticProviderInfo) {
        // Check for environment override
        const envKey = `${provider.toUpperCase()}_BASE_URL`;
        return process.env[envKey] || staticProviderInfo.baseURL;
    }
    // Fallback to environment variable
    const envKey = `${provider.toUpperCase()}_BASE_URL`;
    return process.env[envKey] || 'https://api.openai.com/v1';
}
/**
 * Clear configuration cache
 */
export function clearConfigCache() {
    cachedConfig = null;
}
/**
 * Get configuration file path
 */
export function getConfigPath(global = false) {
    const configPaths = getConfigPaths();
    return global ? configPaths.global : configPaths.project;
}
/**
 * Validate configuration
 */
export function validateConfig(config) {
    const errors = [];
    if (!config.model) {
        errors.push('Model is required');
    }
    if (!config.provider) {
        errors.push('Provider is required');
    }
    if (!['suggest', 'auto', 'auto-edit', 'full-auto', 'manual'].includes(config.approvalMode)) {
        errors.push('Invalid approval mode');
    }
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 200000)) {
        errors.push('Max tokens must be between 1 and 200000');
    }
    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
        errors.push('Temperature must be between 0 and 2');
    }
    if (config.timeout && config.timeout < 1000) {
        errors.push('Timeout must be at least 1000ms');
    }
    if (config.maxIterations && config.maxIterations < 1) {
        errors.push('Max iterations must be at least 1');
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
//# sourceMappingURL=config.js.map