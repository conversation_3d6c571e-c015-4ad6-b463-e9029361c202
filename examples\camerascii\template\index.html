<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CameraASCII - Real-time Webcam to ASCII Art</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>CameraASCII</h1>
            <p>Real-time webcam to ASCII art converter</p>
        </header>

        <main>
            <div class="video-container">
                <div class="video-section">
                    <h3>Original Video</h3>
                    <video id="videoElement" autoplay muted playsinline></video>
                    <canvas id="hiddenCanvas" style="display: none;"></canvas>
                </div>

                <div class="ascii-section">
                    <h3>ASCII Output</h3>
                    <div id="asciiOutput" class="ascii-display"></div>
                </div>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label for="characterSet">Character Set:</label>
                    <select id="characterSet">
                        <option value="simple">Simple ( .:-=+*#%@)</option>
                        <option value="medium" selected>Medium</option>
                        <option value="complex">Complex (Full ASCII)</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="resolution">Resolution:</label>
                    <input type="range" id="resolution" min="20" max="120" value="60">
                    <span id="resolutionValue">60</span>
                </div>

                <div class="control-group">
                    <label for="contrast">Contrast:</label>
                    <input type="range" id="contrast" min="0.5" max="2" step="0.1" value="1">
                    <span id="contrastValue">1.0</span>
                </div>

                <div class="control-group">
                    <label for="brightness">Brightness:</label>
                    <input type="range" id="brightness" min="0.5" max="2" step="0.1" value="1">
                    <span id="brightnessValue">1.0</span>
                </div>

                <div class="control-group">
                    <label for="frameRate">Frame Rate:</label>
                    <input type="range" id="frameRate" min="5" max="30" value="15">
                    <span id="frameRateValue">15 FPS</span>
                </div>

                <div class="control-group">
                    <button id="startButton">Start Camera</button>
                    <button id="stopButton" disabled>Stop Camera</button>
                    <button id="saveButton" disabled>Save ASCII Frame</button>
                </div>
            </div>

            <div class="status">
                <div id="statusMessage">Click "Start Camera" to begin</div>
                <div id="performanceInfo"></div>
            </div>
        </main>

        <footer>
            <p>Built with HTML5, Canvas API, and WebRTC</p>
        </footer>
    </div>

    <script src="ascii-converter.js"></script>
    <script src="camera-handler.js"></script>
    <script src="ui-controls.js"></script>
    <script src="script.js"></script>
</body>
</html>
