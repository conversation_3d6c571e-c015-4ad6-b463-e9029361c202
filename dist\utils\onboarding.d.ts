/**
 * Comprehensive Onboarding System
 *
 * Handles first-time setup, provider configuration, API key management,
 * and seamless transition to interactive mode.
 */
import type { AppConfig } from '../types/index.js';
export interface OnboardingResult {
    success: boolean;
    config: AppConfig;
    needsSetup: boolean;
    error?: string;
}
export interface OnboardingOptions {
    force?: boolean;
    interactive?: boolean;
    provider?: string;
    skipValidation?: boolean;
}
/**
 * Check if onboarding is needed
 */
export declare function needsOnboarding(config?: AppConfig): boolean;
/**
 * Check if onboarding is needed with API key validation
 */
export declare function needsOnboardingWithValidation(config?: AppConfig): Promise<boolean>;
/**
 * Display welcome message
 */
export declare function displayWelcomeMessage(): void;
/**
 * Display provider selection menu
 */
export declare function displayProviderMenu(): void;
/**
 * Get provider choice from user
 */
export declare function getProviderChoice(): Promise<string>;
/**
 * Get API key from user
 */
export declare function getApiKeyFromUser(provider: string): Promise<string>;
/**
 * Validate API key with provider
 */
export declare function validateApiKeyWithProvider(provider: string, apiKey: string): Promise<boolean>;
/**
 * Save API key to environment
 */
export declare function saveApiKeyToEnvironment(provider: string, apiKey: string): void;
/**
 * Get model choice from user
 */
export declare function getModelChoice(provider: string): Promise<string>;
/**
 * Get additional preferences from user
 */
export declare function getAdditionalPreferences(): Promise<Partial<AppConfig>>;
/**
 * Run complete onboarding process
 */
export declare function runOnboarding(options?: OnboardingOptions): Promise<OnboardingResult>;
/**
 * Quick setup for specific provider
 */
export declare function quickSetup(provider: string, apiKey?: string): Promise<OnboardingResult>;
//# sourceMappingURL=onboarding.d.ts.map