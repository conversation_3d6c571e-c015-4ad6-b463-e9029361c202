name: "Prompt Analyzer"
description: "Data science application for prompt clustering and analysis"
technology: ["Python", "Pandas", "Scikit-learn", "Matplotlib", "Streamlit"]
difficulty: "advanced"
estimated_time: "4-6 hours"

prompt: |
  Create a comprehensive prompt analysis and clustering application with the following requirements:

  ## Core Features:
  1. **Data Input**: Support multiple input formats for prompt data
     - CSV file upload
     - JSON file upload
     - Direct text input (one prompt per line)
     - API integration for fetching prompts
  
  2. **Text Preprocessing**: Clean and prepare prompt data
     - Remove special characters and normalize text
     - Handle different encodings
     - Remove duplicates
     - Basic text statistics (length, word count, etc.)
  
  3. **Feature Extraction**: Convert text to numerical features
     - TF-IDF vectorization
     - Word embeddings (Word2Vec, GloVe)
     - Sentence embeddings (BERT, SentenceTransformers)
     - N-gram analysis
  
  4. **Clustering Analysis**: Group similar prompts
     - K-Means clustering
     - Hierarchical clustering
     - DBSCAN for density-based clustering
     - Optimal cluster number detection (elbow method, silhouette analysis)
  
  5. **Visualization**: Interactive charts and plots
     - 2D/3D scatter plots of clusters
     - Word clouds for each cluster
     - Cluster size distribution
     - Feature importance plots
     - Interactive cluster exploration
  
  6. **Statistical Analysis**: Comprehensive prompt analysis
     - Cluster characteristics and summaries
     - Most representative prompts per cluster
     - Outlier detection
     - Similarity matrices
     - Topic modeling (LDA)

  ## Technical Requirements:
  - Python 3.8+ with modern data science stack
  - Streamlit for web interface
  - Efficient processing for large datasets (10k+ prompts)
  - Export results (CSV, JSON, visualizations)
  - Configurable parameters for all algorithms
  - Progress indicators for long-running operations

  ## User Interface:
  1. **Data Upload Page**: File upload and data preview
  2. **Preprocessing Page**: Text cleaning options and statistics
  3. **Feature Engineering Page**: Choose vectorization methods
  4. **Clustering Page**: Algorithm selection and parameter tuning
  5. **Visualization Page**: Interactive plots and exploration
  6. **Analysis Page**: Statistical summaries and insights
  7. **Export Page**: Download results and reports

  ## File Structure:
  ```
  app.py                    # Main Streamlit application
  src/
    data_loader.py          # Data input and loading utilities
    preprocessor.py         # Text preprocessing functions
    feature_extractor.py    # Feature extraction methods
    clustering.py           # Clustering algorithms
    visualizer.py           # Plotting and visualization
    analyzer.py             # Statistical analysis functions
    utils.py                # Helper utilities
  requirements.txt          # Python dependencies
  config.yaml              # Configuration settings
  README.md                # Documentation
  tests/                   # Unit tests
    test_preprocessor.py
    test_clustering.py
    test_analyzer.py
  sample_data/             # Example datasets
    sample_prompts.csv
    sample_prompts.json
  ```

  ## Advanced Features:
  1. **Semantic Search**: Find similar prompts to a query
  2. **Prompt Quality Scoring**: Evaluate prompt effectiveness
  3. **Trend Analysis**: Analyze prompt patterns over time
  4. **Multi-language Support**: Handle prompts in different languages
  5. **Custom Embeddings**: Train domain-specific embeddings
  6. **Batch Processing**: Handle multiple datasets
  7. **API Endpoints**: RESTful API for programmatic access

  ## Machine Learning Pipeline:
  1. Data validation and quality checks
  2. Automated preprocessing with configurable options
  3. Feature extraction with multiple methods
  4. Hyperparameter optimization for clustering
  5. Model evaluation and validation
  6. Results interpretation and reporting

  ## Visualization Requirements:
  - Interactive scatter plots with hover information
  - Hierarchical clustering dendrograms
  - Word clouds with customizable styling
  - Cluster comparison charts
  - Feature importance visualizations
  - Export plots as PNG/SVG/PDF

  ## Performance Considerations:
  - Efficient memory usage for large datasets
  - Parallel processing where applicable
  - Caching for expensive computations
  - Progressive loading for large visualizations
  - Optimized algorithms for real-time interaction

  Please start by creating the basic Streamlit app structure and data loading functionality, then progressively add preprocessing, clustering, and visualization features.

metadata:
  tags: ["data-science", "nlp", "clustering", "visualization", "streamlit"]
  learning_objectives:
    - "Text preprocessing and feature extraction techniques"
    - "Unsupervised machine learning and clustering algorithms"
    - "Data visualization and interactive dashboards"
    - "Streamlit application development"
    - "Statistical analysis and interpretation"
  datasets:
    - "Sample prompt datasets for testing"
    - "Real-world prompt collections"
  evaluation_metrics:
    - "Clustering quality (silhouette score, inertia)"
    - "Visualization clarity and interactivity"
    - "Application performance and usability"
