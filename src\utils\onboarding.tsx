/**
 * Comprehensive Onboarding System
 * 
 * Handles first-time setup, provider configuration, API key management,
 * and seamless transition to interactive mode.
 */

import { existsSync, appendFileSync } from 'fs';
import { homedir } from 'os';
import { join } from 'path';
import chalk from 'chalk';
import { loadConfig, saveConfig, getApiKey, clearConfigCache } from './config.js';
import { getProviderNames, getProvider } from './providers.js';
import { validateApiKey, validateApiKeyDetailed } from './get-api-key.js';
import { logInfo, logError, logWarn } from './logger/log.js';
import type { AppConfig, ProviderConfig } from '../types/index.js';

export interface OnboardingResult {
  success: boolean;
  config: AppConfig;
  needsSetup: boolean;
  error?: string;
}

export interface OnboardingOptions {
  force?: boolean;
  interactive?: boolean;
  provider?: string;
  skipValidation?: boolean;
}

/**
 * Check if onboarding is needed
 */
export function needsOnboarding(config?: AppConfig): boolean {
  const currentConfig = config || loadConfig();

  // Check if we have a valid provider
  if (!currentConfig.provider || !getProvider(currentConfig.provider)) {
    return true;
  }

  // Check if we have an API key for the provider
  const apiKey = getApiKey(currentConfig.provider);
  if (!apiKey && currentConfig.provider !== 'ollama') {
    return true;
  }

  // Check if configuration file exists
  const configPath = getConfigPath();
  if (!existsSync(configPath)) {
    return true;
  }

  return false;
}

/**
 * Check if onboarding is needed with API key validation
 */
export async function needsOnboardingWithValidation(config?: AppConfig): Promise<boolean> {
  const currentConfig = config || loadConfig();

  // Check if we have a valid provider
  if (!currentConfig.provider || !getProvider(currentConfig.provider)) {
    return true;
  }

  // Check if we have an API key for the provider
  const apiKey = getApiKey(currentConfig.provider);
  if (!apiKey && currentConfig.provider !== 'ollama') {
    return true;
  }

  // Check if configuration file exists
  const configPath = getConfigPath();
  if (!existsSync(configPath)) {
    return true;
  }

  // For non-Ollama providers, validate the API key
  if (currentConfig.provider !== 'ollama' && apiKey) {
    try {
      const isValid = await validateApiKey(currentConfig.provider, apiKey);
      if (!isValid) {
        return true; // Invalid API key, need onboarding
      }
    } catch (_error) {
      return true; // Validation failed, need onboarding
    }
  }

  return false;
}

/**
 * Get configuration file path
 */
function getConfigPath(): string {
  return join(homedir(), '.kritrima-ai', 'config.json');
}

/**
 * Display welcome message
 */
export function displayWelcomeMessage(): void {
  console.clear();
  console.log(chalk.cyan('╔══════════════════════════════════════════════════════════════╗'));
  console.log(chalk.cyan('║                 Welcome to Kritrima AI CLI!                 ║'));
  console.log(chalk.cyan('║              Autonomous Coding Assistant                    ║'));
  console.log(chalk.cyan('╚══════════════════════════════════════════════════════════════╝'));
  console.log('');
  console.log(chalk.green('🚀 Let\'s get you set up with your AI coding assistant!'));
  console.log('');
  console.log(chalk.blue('This setup wizard will help you:'));
  console.log(chalk.blue('  • Choose an AI provider'));
  console.log(chalk.blue('  • Configure your API key'));
  console.log(chalk.blue('  • Set up your preferences'));
  console.log(chalk.blue('  • Test your configuration'));
  console.log('');
}

/**
 * Display provider selection menu
 */
export function displayProviderMenu(): void {
  console.log(chalk.cyan('📋 Available AI Providers:'));
  console.log('');
  
  const providers = getProviderNames();
  providers.forEach((providerName, index) => {
    const provider = getProvider(providerName);
    if (provider) {
      const number = chalk.yellow(`${index + 1}.`);
      const name = chalk.green(provider.name);
      const models = provider.models?.slice(0, 3).join(', ') || 'Various models';
      console.log(`${number} ${name}`);
      console.log(`   ${chalk.gray(models)}`);
      console.log('');
    }
  });
}

/**
 * Get provider choice from user
 */
export async function getProviderChoice(): Promise<string> {
  const { default: inquirer } = await import('inquirer');
  const providers = getProviderNames();
  
  const choices = providers.map(providerName => {
    const provider = getProvider(providerName);
    return {
      name: `${provider?.name || providerName} - ${provider?.models?.[0] || 'Various models'}`,
      value: providerName,
      short: provider?.name || providerName,
    };
  });
  
  const { provider } = await inquirer.prompt([
    {
      type: 'list',
      name: 'provider',
      message: 'Choose your AI provider:',
      choices,
      pageSize: 10,
    },
  ]);
  
  return provider;
}

/**
 * Get API key from user
 */
export async function getApiKeyFromUser(provider: string): Promise<string> {
  const { default: inquirer } = await import('inquirer');
  const providerConfig = getProvider(provider);
  
  if (!providerConfig) {
    throw new Error(`Unknown provider: ${provider}`);
  }
  
  console.log('');
  console.log(chalk.cyan(`🔑 API Key Setup for ${providerConfig.name}`));
  console.log('');
  
  // Show instructions for getting API key
  displayApiKeyInstructions(provider, providerConfig);
  
  const { apiKey } = await inquirer.prompt([
    {
      type: 'password',
      name: 'apiKey',
      message: `Enter your ${providerConfig.name} API key:`,
      mask: '*',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'API key is required';
        }
        if (input.length < 10) {
          return 'API key seems too short';
        }
        return true;
      },
    },
  ]);
  
  return apiKey.trim();
}

/**
 * Display API key instructions for provider
 */
function displayApiKeyInstructions(provider: string, config: ProviderConfig): void {
  const instructions: Record<string, string[]> = {
    openai: [
      '1. Go to https://platform.openai.com/account/api-keys',
      '2. Click "Create new secret key"',
      '3. Copy the generated key (starts with sk-)',
    ],
    azure: [
      '1. Go to your Azure OpenAI resource in Azure Portal',
      '2. Navigate to "Keys and Endpoint"',
      '3. Copy one of the API keys',
    ],
    gemini: [
      '1. Go to https://makersuite.google.com/app/apikey',
      '2. Click "Create API key"',
      '3. Copy the generated key',
    ],
    mistral: [
      '1. Go to https://console.mistral.ai/api-keys/',
      '2. Click "Create new key"',
      '3. Copy the generated key',
    ],
    deepseek: [
      '1. Go to https://platform.deepseek.com/api_keys',
      '2. Click "Create API Key"',
      '3. Copy the generated key',
    ],
    xai: [
      '1. Go to https://console.x.ai/api-keys',
      '2. Click "Create API Key"',
      '3. Copy the generated key',
    ],
    groq: [
      '1. Go to https://console.groq.com/keys',
      '2. Click "Create API Key"',
      '3. Copy the generated key',
    ],
    ollama: [
      '1. Install Ollama from https://ollama.ai',
      '2. Run: ollama serve',
      '3. No API key needed for local Ollama',
    ],
  };
  
  const providerInstructions = instructions[provider.toLowerCase()];
  if (providerInstructions) {
    console.log(chalk.blue(`To get your ${config.name} API key:`));
    providerInstructions.forEach(step => {
      console.log(chalk.gray(`  ${step}`));
    });
    console.log('');
  }
}

/**
 * Validate API key with provider
 */
export async function validateApiKeyWithProvider(provider: string, apiKey: string): Promise<boolean> {
  console.log(chalk.blue('🔍 Validating API key...'));

  try {
    // Set environment variable temporarily for validation
    const providerConfig = getProvider(provider);
    if (!providerConfig) {
      console.log(chalk.red(`❌ Unknown provider: ${provider}`));
      return false;
    }

    const envKey = providerConfig.envKey;
    const originalValue = process.env[envKey];
    process.env[envKey] = apiKey;

    logInfo(`Validating API key for ${provider} using model ${providerConfig.defaultModel}`);
    const validationResult = await validateApiKeyDetailed(provider, apiKey);

    // Restore original value
    if (originalValue !== undefined) {
      process.env[envKey] = originalValue;
    } else {
      delete process.env[envKey];
    }

    if (validationResult.isValid) {
      console.log(chalk.green('✅ API key is valid!'));
      return true;
    } else {
      console.log(chalk.red('❌ API key validation failed'));
      if (validationResult.error) {
        console.log(chalk.gray(`Error: ${validationResult.error}`));
      }
      if (validationResult.suggestions && validationResult.suggestions.length > 0) {
        console.log(chalk.yellow('Suggestions:'));
        validationResult.suggestions.forEach(suggestion => {
          console.log(chalk.gray(`  • ${suggestion}`));
        });
      }

      return false;
    }
  } catch (error) {
    console.log(chalk.red('❌ API key validation failed'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.log(chalk.gray(`Error details: ${errorMessage}`));
    logError('API key validation error', error as Error);
    return false;
  }
}

/**
 * Save API key to environment
 */
export function saveApiKeyToEnvironment(provider: string, apiKey: string): void {
  const providerConfig = getProvider(provider);
  if (!providerConfig) {
    throw new Error(`Unknown provider: ${provider}`);
  }
  
  const envKey = providerConfig.envKey;
  process.env[envKey] = apiKey;
  
  // Also save to shell profile for persistence
  saveApiKeyToProfile(envKey, apiKey);
}

/**
 * Save API key to shell profile
 */
function saveApiKeyToProfile(envKey: string, apiKey: string): void {
  const profiles = [
    '.bashrc',
    '.zshrc',
    '.profile',
  ];

  const exportLine = `export ${envKey}="${apiKey}"\n`;

  for (const profile of profiles) {
    const profilePath = join(homedir(), profile);
    if (existsSync(profilePath)) {
      try {
        appendFileSync(profilePath, `\n# Kritrima AI CLI\n${exportLine}`);
        console.log(chalk.green(`✅ API key saved to ${profile}`));
        break;
      } catch (error) {
        logWarn(`Failed to save to ${profile}`, error as Error);
      }
    }
  }
}

/**
 * Get model choice from user
 */
export async function getModelChoice(provider: string): Promise<string> {
  const { default: inquirer } = await import('inquirer');
  const providerConfig = getProvider(provider);

  if (!providerConfig || !providerConfig.models) {
    return providerConfig?.defaultModel || 'gpt-4';
  }

  const choices = providerConfig.models.map(model => ({
    name: model,
    value: model,
  }));

  const { model } = await inquirer.prompt([
    {
      type: 'list',
      name: 'model',
      message: `Choose a model for ${providerConfig.name}:`,
      choices,
      default: providerConfig.defaultModel,
      pageSize: 10,
    },
  ]);

  return model;
}

/**
 * Get additional preferences from user
 */
export async function getAdditionalPreferences(): Promise<Partial<AppConfig>> {
  const { default: inquirer } = await import('inquirer');

  const preferences = await inquirer.prompt([
    {
      type: 'list',
      name: 'approvalMode',
      message: 'Choose approval mode:',
      choices: [
        {
          name: 'Suggest - Manual approval for all actions (Recommended)',
          value: 'suggest',
        },
        {
          name: 'Auto-edit - Automatic file edits, manual command approval',
          value: 'auto-edit',
        },
        {
          name: 'Full-auto - Automatic everything (Use with caution)',
          value: 'full-auto',
        },
      ],
      default: 'suggest',
    },
    {
      type: 'confirm',
      name: 'notifications',
      message: 'Enable desktop notifications?',
      default: true,
    },
    {
      type: 'confirm',
      name: 'saveHistory',
      message: 'Save command history?',
      default: true,
    },
    {
      type: 'confirm',
      name: 'debug',
      message: 'Enable debug logging?',
      default: false,
    },
  ]);

  return preferences;
}

/**
 * Run complete onboarding process
 */
export async function runOnboarding(options: OnboardingOptions = {}): Promise<OnboardingResult> {
  try {
    logInfo('Starting onboarding process');

    // Only skip onboarding if explicitly not forced AND not interactive AND no setup needed
    const shouldSkip = !options.force && !options.interactive && !needsOnboarding();
    if (shouldSkip) {
      console.log(chalk.blue('ℹ️  Configuration already exists. Use --force to override.'));
      return {
        success: true,
        config: loadConfig(),
        needsSetup: false,
      };
    }

    // Display welcome message
    displayWelcomeMessage();

    // Get provider choice
    let provider = options.provider;
    if (!provider) {
      provider = await getProviderChoice();
    }

    logInfo(`Selected provider: ${provider}`);

    // Get API key (skip for Ollama)
    let apiKey = '';
    if (provider !== 'ollama') {
      apiKey = await getApiKeyFromUser(provider);

      // Validate API key unless skipped
      if (!options.skipValidation) {
        const isValid = await validateApiKeyWithProvider(provider, apiKey);
        if (!isValid) {
          const { default: inquirer } = await import('inquirer');
          const { retry } = await inquirer.prompt([
            {
              type: 'confirm',
              name: 'retry',
              message: 'API key validation failed. Would you like to try again?',
              default: true,
            },
          ]);

          if (retry) {
            return runOnboarding(options);
          } else {
            return {
              success: false,
              config: loadConfig(),
              needsSetup: true,
              error: 'API key validation failed',
            };
          }
        }
      }

      // Save API key to environment
      saveApiKeyToEnvironment(provider, apiKey);
    }

    // Get model choice
    const model = await getModelChoice(provider);
    logInfo(`Selected model: ${model}`);

    // Get additional preferences
    const preferences = await getAdditionalPreferences();

    // Create configuration
    const config: AppConfig = {
      provider,
      model,
      approvalMode: preferences.approvalMode || 'suggest',
      maxTokens: 4096,
      temperature: 0.7,
      timeout: 30000,
      debug: preferences.debug || false,
      notifications: preferences.notifications !== false,
      saveHistory: preferences.saveHistory !== false,
      maxHistorySize: 1000,
      maxIterations: 10,
      workingDirectory: process.cwd(),
      additionalWritableRoots: [],
      disableResponseStorage: false,
    };

    // Save configuration
    saveConfig(config, true); // Save globally
    clearConfigCache(); // Clear cache to reload

    console.log('');
    console.log(chalk.green('🎉 Setup completed successfully!'));
    console.log('');
    console.log(chalk.blue('Your configuration:'));
    console.log(chalk.blue(`  Provider: ${config.provider}`));
    console.log(chalk.blue(`  Model: ${config.model}`));
    console.log(chalk.blue(`  Approval Mode: ${config.approvalMode}`));
    console.log('');
    console.log(chalk.green('Starting interactive mode...'));
    console.log('');

    return {
      success: true,
      config,
      needsSetup: false,
    };

  } catch (error) {
    logError('Onboarding failed', error as Error);
    return {
      success: false,
      config: loadConfig(),
      needsSetup: true,
      error: (error as Error).message,
    };
  }
}

/**
 * Quick setup for specific provider
 */
export async function quickSetup(provider: string, apiKey?: string): Promise<OnboardingResult> {
  try {
    logInfo(`Quick setup for provider: ${provider}`);

    // Validate provider
    const providerConfig = getProvider(provider);
    if (!providerConfig) {
      throw new Error(`Unknown provider: ${provider}`);
    }

    // Get API key if not provided
    if (!apiKey && provider !== 'ollama') {
      apiKey = await getApiKeyFromUser(provider);
    }

    // Validate API key
    if (apiKey && provider !== 'ollama') {
      const isValid = await validateApiKeyWithProvider(provider, apiKey);
      if (!isValid) {
        throw new Error('API key validation failed');
      }
      saveApiKeyToEnvironment(provider, apiKey);
    }

    // Create minimal configuration
    const config: AppConfig = {
      provider,
      model: providerConfig.defaultModel || providerConfig.models?.[0] || 'gpt-4',
      approvalMode: 'suggest',
      maxTokens: 4096,
      temperature: 0.7,
      timeout: 30000,
      debug: false,
      notifications: true,
      saveHistory: true,
      maxHistorySize: 1000,
      maxIterations: 10,
      workingDirectory: process.cwd(),
      additionalWritableRoots: [],
      disableResponseStorage: false,
    };

    // Save configuration
    saveConfig(config, true);
    clearConfigCache();

    console.log(chalk.green('✅ Quick setup completed!'));

    return {
      success: true,
      config,
      needsSetup: false,
    };

  } catch (error) {
    logError('Quick setup failed', error as Error);
    return {
      success: false,
      config: loadConfig(),
      needsSetup: true,
      error: (error as Error).message,
    };
  }
}
