/**
 * Debug Validation Utilities
 *
 * Comprehensive debugging tools for API key validation and provider testing.
 */
export interface DebugValidationResult {
    provider: string;
    apiKeyPresent: boolean;
    providerConfigValid: boolean;
    clientCreated: boolean;
    modelsListSuccess: boolean;
    chatCompletionSuccess: boolean;
    error?: string;
    details: string[];
}
/**
 * Comprehensive debug validation for a provider
 */
export declare function debugValidateProvider(provider: string, apiKey: string): Promise<DebugValidationResult>;
/**
 * Print debug validation results
 */
export declare function printDebugResults(result: DebugValidationResult): void;
/**
 * Quick debug test for DeepSeek specifically
 */
export declare function debugDeepSeek(apiKey: string): Promise<void>;
//# sourceMappingURL=debug-validation.d.ts.map