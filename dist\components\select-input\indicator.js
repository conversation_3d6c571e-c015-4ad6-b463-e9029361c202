/**
 * Selection Indicator Component
 *
 * Provides visual indicators for selection states using Unicode symbols
 * with consistent display and accessibility support.
 */
import blessed from 'blessed';
export const INDICATOR_SYMBOLS = {
    checkbox: {
        checked: '☑',
        unchecked: '☐',
        disabled: '☒',
    },
    radio: {
        checked: '●',
        unchecked: '○',
        disabled: '◌',
    },
    arrow: {
        checked: '▶',
        unchecked: '▷',
        disabled: '▷',
    },
    bullet: {
        checked: '•',
        unchecked: '◦',
        disabled: '◦',
    },
    custom: {
        checked: '✓',
        unchecked: ' ',
        disabled: '✗',
    },
};
export class Indicator {
    element;
    options;
    checked;
    disabled;
    constructor(options) {
        this.options = options;
        this.checked = options.checked || false;
        this.disabled = options.disabled || false;
        // Create the element with safe style initialization
        this.element = blessed.box({
            parent: options.parent,
            top: options.top || 0,
            left: options.left || 0,
            width: options.width || 'shrink',
            height: options.height || 1,
            style: {
                fg: 'white', // Default color, will be updated in updateDisplay
                ...options.style,
            },
            hidden: options.hidden,
            tags: false,
        });
        // Ensure style object exists
        if (!this.element.style) {
            this.element.style = {};
        }
        this.updateDisplay();
    }
    getSymbols() {
        const type = this.options.type || 'checkbox';
        return this.options.customSymbols || INDICATOR_SYMBOLS[type];
    }
    getColor() {
        if (this.disabled) {
            return 'gray';
        }
        return this.checked ? 'green' : 'white';
    }
    updateDisplay() {
        const symbols = this.getSymbols();
        let symbol;
        if (this.disabled) {
            symbol = symbols.disabled;
        }
        else if (this.checked) {
            symbol = symbols.checked;
        }
        else {
            symbol = symbols.unchecked;
        }
        let content = symbol;
        if (this.options.label) {
            content += ` ${this.options.label}`;
        }
        this.element.setContent(content);
        // Ensure style object exists before accessing properties
        if (!this.element.style) {
            this.element.style = {};
        }
        // Safely set the foreground color
        try {
            this.element.style.fg = this.getColor();
        }
        catch (_error) {
            // Fallback to default color if setting fails
            this.element.style.fg = 'white';
        }
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    // Public API
    isChecked() {
        return this.checked;
    }
    setChecked(checked) {
        if (this.disabled) {
            return;
        }
        this.checked = checked;
        this.updateDisplay();
    }
    toggle() {
        this.setChecked(!this.checked);
    }
    isDisabled() {
        return this.disabled;
    }
    setDisabled(disabled) {
        this.disabled = disabled;
        this.updateDisplay();
    }
    setLabel(label) {
        this.options.label = label;
        this.updateDisplay();
    }
    getLabel() {
        return this.options.label;
    }
    setType(type) {
        this.options.type = type;
        this.updateDisplay();
    }
    getType() {
        return this.options.type;
    }
    setCustomSymbols(symbols) {
        this.options.customSymbols = symbols;
        this.updateDisplay();
    }
    show() {
        this.element.show();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    hide() {
        this.element.hide();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    focus() {
        this.element.focus();
    }
    blur() {
        this.element.blur?.();
    }
    destroy() {
        this.element.destroy();
    }
    getElement() {
        return this.element;
    }
    setPosition(top, left) {
        this.element.top = top;
        this.element.left = left;
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    setSize(width, height) {
        this.element.width = width;
        this.element.height = height;
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
}
/**
 * Create a checkbox indicator
 */
export function createCheckbox(parent, options = {}) {
    return new Indicator({
        parent,
        type: 'checkbox',
        ...options,
    });
}
/**
 * Create a radio button indicator
 */
export function createRadio(parent, options = {}) {
    return new Indicator({
        parent,
        type: 'radio',
        ...options,
    });
}
/**
 * Create an arrow indicator
 */
export function createArrow(parent, options = {}) {
    return new Indicator({
        parent,
        type: 'arrow',
        ...options,
    });
}
/**
 * Create a bullet indicator
 */
export function createBullet(parent, options = {}) {
    return new Indicator({
        parent,
        type: 'bullet',
        ...options,
    });
}
/**
 * Create a group of radio indicators that work together
 */
export class RadioGroup {
    indicators = [];
    selectedIndex = -1;
    constructor(parent, options, groupOptions = {}) {
        options.forEach((option, index) => {
            const indicator = new Indicator({
                parent,
                type: 'radio',
                label: option.label,
                disabled: option.disabled,
                top: (groupOptions.top || 0) + index,
                left: groupOptions.left,
                width: groupOptions.width,
                ...groupOptions,
            });
            // Add click handler
            indicator.getElement().on('click', () => {
                this.select(index);
            });
            this.indicators.push(indicator);
        });
    }
    select(index) {
        if (index < 0 || index >= this.indicators.length) {
            return;
        }
        if (this.indicators[index].isDisabled()) {
            return;
        }
        // Uncheck all others
        this.indicators.forEach((indicator, i) => {
            indicator.setChecked(i === index);
        });
        this.selectedIndex = index;
    }
    getSelected() {
        return this.selectedIndex;
    }
    getSelectedIndicator() {
        return this.selectedIndex >= 0 ? this.indicators[this.selectedIndex] : null;
    }
    setDisabled(index, disabled) {
        if (index >= 0 && index < this.indicators.length) {
            this.indicators[index].setDisabled(disabled);
        }
    }
    destroy() {
        this.indicators.forEach(indicator => indicator.destroy());
        this.indicators = [];
    }
    getIndicators() {
        return [...this.indicators];
    }
}
/**
 * Create a group of checkbox indicators
 */
export class CheckboxGroup {
    indicators = [];
    constructor(parent, options, groupOptions = {}) {
        options.forEach((option, index) => {
            const indicator = new Indicator({
                parent,
                type: 'checkbox',
                label: option.label,
                checked: option.checked,
                disabled: option.disabled,
                top: (groupOptions.top || 0) + index,
                left: groupOptions.left,
                width: groupOptions.width,
                ...groupOptions,
            });
            // Add click handler
            indicator.getElement().on('click', () => {
                if (!indicator.isDisabled()) {
                    indicator.toggle();
                }
            });
            this.indicators.push(indicator);
        });
    }
    getChecked() {
        return this.indicators
            .map((indicator, index) => ({ indicator, index }))
            .filter(({ indicator }) => indicator.isChecked())
            .map(({ index }) => index);
    }
    getCheckedIndicators() {
        return this.indicators.filter(indicator => indicator.isChecked());
    }
    setChecked(index, checked) {
        if (index >= 0 && index < this.indicators.length) {
            this.indicators[index].setChecked(checked);
        }
    }
    setDisabled(index, disabled) {
        if (index >= 0 && index < this.indicators.length) {
            this.indicators[index].setDisabled(disabled);
        }
    }
    checkAll() {
        this.indicators.forEach(indicator => {
            if (!indicator.isDisabled()) {
                indicator.setChecked(true);
            }
        });
    }
    uncheckAll() {
        this.indicators.forEach(indicator => indicator.setChecked(false));
    }
    destroy() {
        this.indicators.forEach(indicator => indicator.destroy());
        this.indicators = [];
    }
    getIndicators() {
        return [...this.indicators];
    }
}
export default Indicator;
//# sourceMappingURL=indicator.js.map