/**
 * Interactive Configuration Wizard
 *
 * Provides a comprehensive interface for configuring providers,
 * API keys, models, and preferences with validation and testing.
 */
import type { AppConfig } from '../types/index.js';
export interface WizardOptions {
    provider?: string;
    skipApiKey?: boolean;
    skipValidation?: boolean;
    global?: boolean;
}
export interface WizardResult {
    success: boolean;
    config: AppConfig;
    error?: string;
}
/**
 * Main configuration wizard
 */
export declare class ConfigurationWizard {
    private config;
    private options;
    constructor(options?: WizardOptions);
    /**
     * Run the complete configuration wizard
     */
    run(): Promise<WizardResult>;
    /**
     * Display wizard header
     */
    private displayHeader;
    /**
     * Provider selection step
     */
    private selectProvider;
    /**
     * API key configuration step
     */
    private configureApiKey;
    /**
     * Display API key instructions
     */
    private displayApiKeyInstructions;
    /**
     * Model selection step
     */
    private selectModel;
    /**
     * Preferences configuration step
     */
    private configurePreferences;
    /**
     * Test configuration step
     */
    private testConfiguration;
    /**
     * Save configuration step
     */
    private saveConfiguration;
    /**
     * Display success message
     */
    private displaySuccess;
}
/**
 * Run configuration wizard
 */
export declare function runConfigurationWizard(options?: WizardOptions): Promise<WizardResult>;
//# sourceMappingURL=config-wizard.d.ts.map