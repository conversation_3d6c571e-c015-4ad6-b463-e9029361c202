/**
 * Terminal Chat Response Item Component
 * 
 * Renders individual chat messages with syntax highlighting,
 * code blocks, streaming support, and interactive elements.
 */

import blessed from 'blessed';
import { formatCommandForDisplay } from '../../format-command.js';
import { formatDuration } from '../../format-command.js';
import type { ResponseItem, ResponseOutputItem } from '../../types/index.js';

export interface ResponseItemOptions {
  parent: blessed.Widgets.Node;
  item: ResponseItem | ResponseOutputItem;
  width?: string | number;
  onCommandClick?: (command: string[]) => void;
  onFileClick?: (filePath: string) => void;
  showTimestamp?: boolean;
  compact?: boolean;
}

export class TerminalChatResponseItem {
  private container: blessed.Widgets.BoxElement;
  private options: ResponseItemOptions;
  private item: ResponseItem | ResponseOutputItem;

  constructor(options: ResponseItemOptions) {
    this.options = options;
    this.item = options.item;

    this.container = blessed.box({
      parent: options.parent,
      width: options.width || '100%',
      height: 'shrink',
      padding: {
        top: 0,
        bottom: 1,
        left: 1,
        right: 1,
      },
      style: {
        fg: 'white',
        bg: 'black',
      },
      tags: true,
      scrollable: true,
      mouse: true,
    });

    this.render();
  }

  /**
   * Render the response item
   */
  private render(): void {
    const content = this.buildContent();
    this.container.setContent(content);
  }

  /**
   * Build content based on item type
   */
  private buildContent(): string {
    const lines: string[] = [];

    // Add timestamp if enabled
    if (this.options.showTimestamp && this.item.timestamp) {
      const timestamp = new Date(this.item.timestamp).toLocaleTimeString();
      lines.push(`{gray-fg}[${timestamp}]{/gray-fg}`);
    }

    // Render based on item type
    switch (this.item.type) {
    case 'message':
      lines.push(...this.renderMessage());
      break;
    case 'function_call':
      lines.push(...this.renderFunctionCall());
      break;
    case 'error':
      lines.push(...this.renderError());
      break;
    case 'command_output' as any:
      lines.push(...this.renderCommandOutput());
      break;
    default:
      lines.push(`{yellow-fg}Unknown item type: ${this.item.type}{/yellow-fg}`);
    }

    return lines.join('\n');
  }

  /**
   * Render message content
   */
  private renderMessage(): string[] {
    const lines: string[] = [];
    
    if (!this.item.content || this.item.content.length === 0) {
      return lines;
    }

    for (const content of this.item.content) {
      switch (content.type) {
      case 'input_text':
        lines.push(...this.renderInputText(content.text || ''));
        break;
      case 'output_text':
        lines.push(...this.renderOutputText(content.text || ''));
        break;
      case 'tool_use' as any:
        lines.push(...this.renderToolUse(content));
        break;
      case 'tool_result' as any:
        lines.push(...this.renderToolResult(content));
        break;
      }
    }

    return lines;
  }

  /**
   * Render input text (user message)
   */
  private renderInputText(text: string): string[] {
    const lines: string[] = [];
    
    lines.push('{cyan-fg}❯{/cyan-fg} {bold}You:{/bold}');
    
    // Process text for file tags and commands
    const processedText = this.processTextContent(text);
    lines.push(...this.wrapText(processedText, 2));
    
    return lines;
  }

  /**
   * Render output text (AI response)
   */
  private renderOutputText(text: string): string[] {
    const lines: string[] = [];
    
    lines.push('{green-fg}🤖{/green-fg} {bold}Assistant:{/bold}');
    
    // Process markdown-like formatting
    const processedText = this.processMarkdown(text);
    lines.push(...this.wrapText(processedText, 2));
    
    return lines;
  }

  /**
   * Render tool use
   */
  private renderToolUse(content: any): string[] {
    const lines: string[] = [];
    
    lines.push(`{yellow-fg}🔧{/yellow-fg} {bold}Tool: ${content.name}{/bold}`);
    
    if (content.input) {
      lines.push('{gray-fg}Input:{/gray-fg}');
      const inputStr = typeof content.input === 'string' ? 
        content.input : 
        JSON.stringify(content.input, null, 2);
      lines.push(...this.wrapText(inputStr, 2));
    }
    
    return lines;
  }

  /**
   * Render tool result
   */
  private renderToolResult(content: any): string[] {
    const lines: string[] = [];
    
    const isError = content.is_error || false;
    const icon = isError ? '❌' : '✅';
    const color = isError ? 'red-fg' : 'green-fg';
    
    lines.push(`{${color}}${icon}{/${color}} {bold}Result:{/bold}`);
    
    if (content.content) {
      const resultStr = typeof content.content === 'string' ? 
        content.content : 
        JSON.stringify(content.content, null, 2);
      lines.push(...this.wrapText(resultStr, 2));
    }
    
    return lines;
  }

  /**
   * Render function call
   */
  private renderFunctionCall(): string[] {
    const lines: string[] = [];
    
    if ('function_name' in this.item) {
      lines.push(`{blue-fg}⚡{/blue-fg} {bold}Function Call: ${this.item.function_name}{/bold}`);
      
      if ((this.item as any).arguments) {
        lines.push('{gray-fg}Arguments:{/gray-fg}');
        const argsStr = JSON.stringify((this.item as any).arguments, null, 2);
        lines.push(...this.wrapText(argsStr, 2));
      }
    }
    
    return lines;
  }

  /**
   * Render error
   */
  private renderError(): string[] {
    const lines: string[] = [];
    
    lines.push('{red-fg}❌{/red-fg} {bold}Error:{/bold}');
    
    if ('error' in this.item && this.item.error) {
      const errorText = typeof this.item.error === 'string' ? 
        this.item.error : 
        (this.item.error as any)?.message || 'Unknown error';
      lines.push(...this.wrapText(`{red-fg}${errorText}{/red-fg}`, 2));
    }
    
    return lines;
  }

  /**
   * Render command output
   */
  private renderCommandOutput(): string[] {
    const lines: string[] = [];
    
    if ('command' in this.item && this.item.command) {
      const commandStr = formatCommandForDisplay(this.item.command);
      lines.push(`{magenta-fg}$>{/magenta-fg} {bold}${commandStr}{/bold}`);
      
      if ('output' in this.item && this.item.output) {
        lines.push('{gray-fg}Output:{/gray-fg}');
        lines.push(...this.wrapText(this.item.output, 2));
      }
      
      if ('exit_code' in this.item && this.item.exit_code !== undefined) {
        const exitCode = this.item.exit_code;
        const color = exitCode === 0 ? 'green-fg' : 'red-fg';
        lines.push(`{${color}}Exit code: ${exitCode}{/${color}}`);
      }
      
      if ('duration' in this.item && this.item.duration) {
        const duration = formatDuration(this.item.duration);
        lines.push(`{gray-fg}Duration: ${duration}{/gray-fg}`);
      }
    }
    
    return lines;
  }

  /**
   * Process text content for special formatting
   */
  private processTextContent(text: string): string {
    let processed = text;
    
    // Highlight file tags
    processed = processed.replace(/@([^\s@]+)/g, '{cyan-fg}@$1{/cyan-fg}');
    
    // Highlight slash commands
    processed = processed.replace(/^\/\w+/gm, '{yellow-fg}$&{/yellow-fg}');
    
    return processed;
  }

  /**
   * Process markdown-like formatting
   */
  private processMarkdown(text: string): string {
    let processed = text;
    
    // Code blocks
    processed = processed.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
      const lines = code.split('\n').map(line => `  ${line}`);
      return `{gray-fg}┌─ Code${lang ? ` (${lang})` : ''} ─{/gray-fg}\n{cyan-fg}${lines.join('\n')}{/cyan-fg}\n{gray-fg}└─────{/gray-fg}`;
    });
    
    // Inline code
    processed = processed.replace(/`([^`]+)`/g, '{cyan-fg}$1{/cyan-fg}');
    
    // Bold text
    processed = processed.replace(/\*\*([^*]+)\*\*/g, '{bold}$1{/bold}');
    
    // Italic text (using underline as approximation)
    processed = processed.replace(/\*([^*]+)\*/g, '{underline}$1{/underline}');
    
    // Headers
    processed = processed.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
      const level = hashes.length;
      const color = level <= 2 ? 'yellow-fg' : 'white-fg';
      return `{${color}}{bold}${title}{/bold}{/${color}}`;
    });
    
    // Lists
    processed = processed.replace(/^[\s]*[-*+]\s+(.+)$/gm, '{gray-fg}•{/gray-fg} $1');
    processed = processed.replace(/^[\s]*\d+\.\s+(.+)$/gm, (match, item, offset, string) => {
      const lineStart = string.lastIndexOf('\n', offset) + 1;
      const lineText = string.slice(lineStart, offset);
      const _indent = lineText.match(/^\s*/)?.[0] || '';
      const number = match.match(/\d+/)?.[0] || '1';
      return `{gray-fg}${number}.{/gray-fg} ${item}`;
    });
    
    return processed;
  }

  /**
   * Wrap text to fit width with indentation
   */
  private wrapText(text: string, indent = 0): string[] {
    const lines: string[] = [];
    const indentStr = ' '.repeat(indent);
    const maxWidth = (this.container.width as number || 80) - indent - 4; // Account for padding
    
    for (const line of text.split('\n')) {
      if (line.length <= maxWidth) {
        lines.push(indentStr + line);
      } else {
        // Simple word wrapping
        const words = line.split(' ');
        let currentLine = indentStr;
        
        for (const word of words) {
          if (currentLine.length + word.length + 1 <= maxWidth + indent) {
            currentLine += (currentLine === indentStr ? '' : ' ') + word;
          } else {
            if (currentLine !== indentStr) {
              lines.push(currentLine);
            }
            currentLine = indentStr + word;
          }
        }
        
        if (currentLine !== indentStr) {
          lines.push(currentLine);
        }
      }
    }
    
    return lines;
  }

  /**
   * Update the item content
   */
  updateItem(item: ResponseItem | ResponseOutputItem): void {
    this.item = item;
    this.render();
    
    if (this.container.screen) {
      this.container.screen.render();
    }
  }

  /**
   * Get the container element
   */
  getElement(): blessed.Widgets.BoxElement {
    return this.container;
  }

  /**
   * Get the item height
   */
  getHeight(): number {
    return this.container.height as number || 0;
  }

  /**
   * Scroll to make this item visible
   */
  scrollIntoView(): void {
    if (this.container.parent && 'scrollTo' in this.container.parent) {
      (this.container.parent as any).scrollTo(this.container.atop || 0);
    }
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    this.container.destroy();
  }
}

/**
 * Create a response item component
 */
export function createResponseItem(
  parent: blessed.Widgets.Node,
  item: ResponseItem | ResponseOutputItem,
  options: Partial<ResponseItemOptions> = {},
): TerminalChatResponseItem {
  return new TerminalChatResponseItem({
    parent,
    item,
    ...options,
  });
}

export default TerminalChatResponseItem;
