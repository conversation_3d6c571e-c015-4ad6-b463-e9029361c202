/**
 * Select Input Component
 * 
 * Provides a dropdown-style selection interface with keyboard navigation,
 * search functionality, and customizable rendering.
 */

import blessed from 'blessed';
import { EventEmitter } from 'events';
import type { BlessedStyle, BlessedBorder, BlessedKeyEvent } from '../../types/blessed-extensions.js';

export interface SelectOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
  group?: string;
}

export interface SelectInputOptions {
  parent: blessed.Widgets.Node;
  top?: string | number;
  left?: string | number;
  width?: string | number;
  height?: string | number;
  options: SelectOption[];
  placeholder?: string;
  searchable?: boolean;
  multiSelect?: boolean;
  maxHeight?: number;
  style?: BlessedStyle;
  border?: BlessedBorder;
  label?: string;
  hidden?: boolean;
  onSelect?: (option: SelectOption | SelectOption[]) => void;
  onChange?: (value: string | string[]) => void;
  onSearch?: (query: string) => void;
}

export class SelectInput extends EventEmitter {
  private container: blessed.Widgets.BoxElement;
  private inputBox: blessed.Widgets.TextareaElement;
  private listBox: blessed.Widgets.ListElement;
  private options: SelectInputOptions;
  private allOptions: SelectOption[];
  private filteredOptions: SelectOption[];
  private selectedOptions: Set<string> = new Set();
  private isOpen = false;
  private searchQuery = '';

  constructor(options: SelectInputOptions) {
    super();
    this.options = options;
    this.allOptions = [...options.options];
    this.filteredOptions = [...options.options];

    this.createContainer();
    this.createInputBox();
    this.createListBox();
    this.setupEventHandlers();
    this.updateDisplay();
  }

  private createContainer(): void {
    this.container = blessed.box({
      parent: this.options.parent,
      top: this.options.top || 0,
      left: this.options.left || 0,
      width: this.options.width || '100%',
      height: this.options.height || 'shrink',
      border: this.options.border || { type: 'line' },
      style: {
        fg: 'white',
        bg: 'black',
        border: { fg: 'cyan' },
        focus: { border: { fg: 'yellow' } },
        ...this.options.style,
      },
      label: this.options.label,
      hidden: this.options.hidden,
      tags: true,
    });
  }

  private createInputBox(): void {
    this.inputBox = blessed.textarea({
      parent: this.container,
      top: 0,
      left: 0,
      width: '100%',
      height: 1,
      style: {
        fg: 'white',
        bg: 'black',
      },
      inputOnFocus: true,
      keys: true,
      mouse: true,
      tags: false,
    });

    this.updateInputDisplay();
  }

  private createListBox(): void {
    this.listBox = blessed.list({
      parent: this.container,
      top: 1,
      left: 0,
      width: '100%',
      height: Math.min(this.options.maxHeight || 10, this.filteredOptions.length + 1),
      border: { type: 'line' },
      style: {
        fg: 'white',
        bg: 'black',
        border: { fg: 'gray' },
        selected: { bg: 'blue', fg: 'white' },
        item: { fg: 'white' },
      },
      keys: true,
      mouse: true,
      scrollable: true,
      alwaysScroll: true,
      hidden: true,
      tags: true,
    });

    this.updateListItems();
  }

  private setupEventHandlers(): void {
    // Input box events
    this.inputBox.on('keypress', (ch, key) => {
      this.handleInputKeyPress(ch, key);
    });

    this.inputBox.key(['enter'], () => {
      if (this.isOpen) {
        this.selectCurrentItem();
      } else {
        this.open();
      }
    });

    this.inputBox.key(['escape'], () => {
      this.close();
    });

    this.inputBox.key(['down'], () => {
      if (this.isOpen) {
        this.listBox.down(1);
        this.render();
      } else {
        this.open();
      }
    });

    this.inputBox.key(['up'], () => {
      if (this.isOpen) {
        this.listBox.up(1);
        this.render();
      }
    });

    this.inputBox.key(['tab'], () => {
      if (this.isOpen) {
        this.selectCurrentItem();
      } else {
        this.open();
      }
    });

    // List box events
    this.listBox.on('select', (item, index) => {
      this.selectOption(index);
    });

    this.listBox.key(['enter', 'space'], () => {
      this.selectCurrentItem();
    });

    this.listBox.key(['escape'], () => {
      this.close();
    });

    // Focus management
    this.inputBox.on('focus', () => {
      this.emit('focus');
    });

    this.inputBox.on('blur', () => {
      // Delay close to allow list selection
      setTimeout(() => {
        if (!(this.listBox as any).focused) {
          this.close();
        }
      }, 100);
    });

    this.listBox.on('blur', () => {
      setTimeout(() => {
        if (!(this.inputBox as any).focused) {
          this.close();
        }
      }, 100);
    });
  }

  private handleInputKeyPress(ch: string, key: BlessedKeyEvent): void {
    if (!this.options.searchable) {return;}

    if (key.name === 'backspace') {
      this.searchQuery = this.searchQuery.slice(0, -1);
    } else if (ch && ch.length === 1 && !key.ctrl && !key.meta) {
      this.searchQuery += ch;
    }

    this.filterOptions();
    this.updateInputDisplay();
    this.updateListItems();

    if (this.options.onSearch) {
      this.options.onSearch(this.searchQuery);
    }

    if (!this.isOpen && this.searchQuery) {
      this.open();
    }
  }

  private filterOptions(): void {
    if (!this.searchQuery) {
      this.filteredOptions = [...this.allOptions];
      return;
    }

    const query = this.searchQuery.toLowerCase();
    this.filteredOptions = this.allOptions.filter(option => 
      option.label.toLowerCase().includes(query) ||
      option.value.toLowerCase().includes(query) ||
      (option.description && option.description.toLowerCase().includes(query)),
    );
  }

  private updateInputDisplay(): void {
    let displayText = '';

    if (this.options.multiSelect && this.selectedOptions.size > 0) {
      const selectedLabels = Array.from(this.selectedOptions)
        .map(value => this.allOptions.find(opt => opt.value === value)?.label || value)
        .join(', ');
      displayText = selectedLabels;
    } else if (!this.options.multiSelect && this.selectedOptions.size === 1) {
      const selectedValue = Array.from(this.selectedOptions)[0];
      const selectedOption = this.allOptions.find(opt => opt.value === selectedValue);
      displayText = selectedOption?.label || selectedValue;
    } else if (this.searchQuery) {
      displayText = this.searchQuery;
    } else {
      displayText = this.options.placeholder || 'Select an option...';
    }

    this.inputBox.setValue(displayText);
  }

  private updateListItems(): void {
    const items: string[] = [];

    // Group options if needed
    const groupedOptions = this.groupOptions();

    for (const [group, options] of groupedOptions) {
      if (group && groupedOptions.size > 1) {
        items.push(`{bold}${group}{/bold}`);
      }

      for (const option of options) {
        let itemText = option.label;
        
        if (option.description) {
          itemText += ` {gray-fg}(${option.description}){/gray-fg}`;
        }

        if (option.disabled) {
          itemText = `{gray-fg}${itemText}{/gray-fg}`;
        }

        if (this.options.multiSelect && this.selectedOptions.has(option.value)) {
          itemText = `{green-fg}✓{/green-fg} ${itemText}`;
        }

        items.push(itemText);
      }
    }

    this.listBox.setItems(items);
    this.listBox.height = Math.min(this.options.maxHeight || 10, items.length + 1);
  }

  private groupOptions(): Map<string, SelectOption[]> {
    const groups = new Map<string, SelectOption[]>();

    for (const option of this.filteredOptions) {
      const group = option.group || '';
      if (!groups.has(group)) {
        groups.set(group, []);
      }
      groups.get(group)!.push(option);
    }

    return groups;
  }

  private selectCurrentItem(): void {
    const selectedIndex = (this.listBox as any).selected || 0;
    this.selectOption(selectedIndex);
  }

  private selectOption(index: number): void {
    // Calculate actual option index considering groups
    let currentIndex = 0;
    const groupedOptions = this.groupOptions();

    for (const [group, options] of groupedOptions) {
      if (group && groupedOptions.size > 1) {
        if (currentIndex === index) {return;} // Selected a group header
        currentIndex++;
      }

      for (const option of options) {
        if (currentIndex === index) {
          if (option.disabled) {return;}

          if (this.options.multiSelect) {
            if (this.selectedOptions.has(option.value)) {
              this.selectedOptions.delete(option.value);
            } else {
              this.selectedOptions.add(option.value);
            }
            this.updateInputDisplay();
            this.updateListItems();
            
            const selectedValues = Array.from(this.selectedOptions);
            const selectedOptions = selectedValues.map(value => 
              this.allOptions.find(opt => opt.value === value)!,
            );
            
            if (this.options.onSelect) {
              this.options.onSelect(selectedOptions);
            }
            if (this.options.onChange) {
              this.options.onChange(selectedValues);
            }
            this.emit('change', selectedValues);
          } else {
            this.selectedOptions.clear();
            this.selectedOptions.add(option.value);
            this.updateInputDisplay();
            this.close();
            
            if (this.options.onSelect) {
              this.options.onSelect(option);
            }
            if (this.options.onChange) {
              this.options.onChange(option.value);
            }
            this.emit('change', option.value);
          }
          return;
        }
        currentIndex++;
      }
    }
  }

  private open(): void {
    if (this.isOpen) {return;}

    this.isOpen = true;
    this.listBox.show();
    this.listBox.focus();
    this.render();
    this.emit('open');
  }

  private close(): void {
    if (!this.isOpen) {return;}

    this.isOpen = false;
    this.listBox.hide();
    this.inputBox.focus();
    this.searchQuery = '';
    this.filterOptions();
    this.updateInputDisplay();
    this.render();
    this.emit('close');
  }

  private render(): void {
    if (this.container.screen) {
      this.container.screen.render();
    }
  }

  /**
   * Update the display (combines input and list updates)
   */
  private updateDisplay(): void {
    this.updateInputDisplay();
    this.updateListItems();
    this.render();
  }

  // Public API
  getSelectedValue(): string | string[] | null {
    if (this.selectedOptions.size === 0) {return null;}
    
    const values = Array.from(this.selectedOptions);
    return this.options.multiSelect ? values : values[0];
  }

  getSelectedOption(): SelectOption | SelectOption[] | null {
    const values = this.getSelectedValue();
    if (!values) {return null;}

    if (Array.isArray(values)) {
      return values.map(value => this.allOptions.find(opt => opt.value === value)!);
    } else {
      return this.allOptions.find(opt => opt.value === values) || null;
    }
  }

  setSelectedValue(value: string | string[]): void {
    this.selectedOptions.clear();
    
    if (Array.isArray(value)) {
      value.forEach(v => this.selectedOptions.add(v));
    } else {
      this.selectedOptions.add(value);
    }
    
    this.updateInputDisplay();
    this.updateListItems();
    this.render();
  }

  setOptions(options: SelectOption[]): void {
    this.allOptions = [...options];
    this.filterOptions();
    this.updateListItems();
    this.render();
  }

  addOption(option: SelectOption): void {
    this.allOptions.push(option);
    this.filterOptions();
    this.updateListItems();
    this.render();
  }

  removeOption(value: string): void {
    this.allOptions = this.allOptions.filter(opt => opt.value !== value);
    this.selectedOptions.delete(value);
    this.filterOptions();
    this.updateInputDisplay();
    this.updateListItems();
    this.render();
  }

  clear(): void {
    this.selectedOptions.clear();
    this.searchQuery = '';
    this.filterOptions();
    this.updateInputDisplay();
    this.updateListItems();
    this.render();
  }

  focus(): void {
    this.inputBox.focus();
  }

  blur(): void {
    (this.inputBox as any).blur?.();
    this.close();
  }

  show(): void {
    this.container.show();
    this.render();
  }

  hide(): void {
    this.container.hide();
    this.close();
    this.render();
  }

  destroy(): void {
    this.close();
    this.container.destroy();
  }

  getElement(): blessed.Widgets.BoxElement {
    return this.container;
  }
}

export default SelectInput;
