#!/bin/bash

# Prompt Analyzer Project Runner
# Creates a new run directory and launches Kritrima AI CLI with the project task

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"

# Create runs directory if it doesn't exist
RUNS_DIR="$PROJECT_DIR/runs"
mkdir -p "$RUNS_DIR"

# Find the next run number
RUN_NUM=1
while [ -d "$RUNS_DIR/run_$RUN_NUM" ]; do
    RUN_NUM=$((RUN_NUM + 1))
done

# Create new run directory
RUN_DIR="$RUNS_DIR/run_$RUN_NUM"
mkdir -p "$RUN_DIR"

echo "Creating Prompt Analyzer project run #$RUN_NUM in $RUN_DIR"

# Copy template files to run directory
if [ -d "$PROJECT_DIR/template" ]; then
    cp -r "$PROJECT_DIR/template/"* "$RUN_DIR/"
    echo "Template files copied to run directory"
fi

# Change to run directory
cd "$RUN_DIR"

# Create Python virtual environment
echo "Setting up Python virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install basic dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "Installing Python dependencies..."
    pip install -r requirements.txt
fi

# Read the task from task.yaml if it exists
TASK_FILE="$PROJECT_DIR/task.yaml"
if [ -f "$TASK_FILE" ]; then
    echo "Loading task from $TASK_FILE"
    
    # Extract the prompt from the YAML file (simple extraction)
    PROMPT=$(grep -A 1000 "prompt:" "$TASK_FILE" | tail -n +2 | sed 's/^  //')
    
    if [ -n "$PROMPT" ]; then
        echo "Starting Kritrima AI CLI with Prompt Analyzer task..."
        echo "Working directory: $RUN_DIR"
        echo "Python virtual environment: activated"
        echo ""
        
        # Launch Kritrima AI CLI with the task prompt
        kritrima-ai "$PROMPT"
    else
        echo "Error: Could not extract prompt from task.yaml"
        exit 1
    fi
else
    echo "Error: task.yaml not found at $TASK_FILE"
    echo "Please create a task.yaml file with the project prompt"
    exit 1
fi
