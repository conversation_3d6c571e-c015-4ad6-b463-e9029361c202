# Prompt Analyzer Dependencies

# Core Data Science Stack
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0
scipy>=1.10.0

# Web Framework
streamlit>=1.28.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
wordcloud>=1.9.0

# Natural Language Processing
nltk>=3.8.0
spacy>=3.6.0
sentence-transformers>=2.2.0
transformers>=4.30.0
gensim>=4.3.0

# Text Processing
textblob>=0.17.0
langdetect>=1.0.9
unidecode>=1.3.0

# Machine Learning Extensions
umap-learn>=0.5.3
hdbscan>=0.8.29
yellowbrick>=1.5.0

# Data Handling
openpyxl>=3.1.0
xlrd>=2.0.1
python-docx>=0.8.11
PyPDF2>=3.0.1

# Utilities
tqdm>=4.65.0
joblib>=1.3.0
pyyaml>=6.0.0
python-dotenv>=1.0.0

# Development and Testing
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Optional: GPU acceleration (uncomment if needed)
# torch>=2.0.0
# tensorflow>=2.13.0
