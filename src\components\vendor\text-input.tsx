/**
 * Advanced Text Input Component
 * 
 * Provides enhanced text input functionality with cursor management,
 * keyboard shortcuts, line continuation, text selection, and paste handling.
 */

import blessed from 'blessed';
import { EventEmitter } from 'events';
import type { BlessedStyle, BlessedBorder, BlessedKeyEvent } from '../../types/blessed-extensions.js';

export interface TextInputOptions {
  parent: blessed.Widgets.Node;
  top?: string | number;
  left?: string | number;
  width?: string | number;
  height?: string | number;
  placeholder?: string;
  multiline?: boolean;
  maxLength?: number;
  style?: BlessedStyle;
  border?: BlessedBorder;
  label?: string;
  hidden?: boolean;
  password?: boolean;
  onSubmit?: (value: string) => void;
  onChange?: (value: string) => void;
  onKeyPress?: (ch: string, key: BlessedKeyEvent) => void;
}

export class TextInput extends EventEmitter {
  private element: blessed.Widgets.TextareaElement;
  private options: TextInputOptions;
  private cursorPosition = 0;
  private selectionStart = -1;
  private selectionEnd = -1;
  private history: string[] = [];
  private historyIndex = -1;
  private clipboard = '';

  constructor(options: TextInputOptions) {
    super();
    this.options = options;

    this.element = blessed.textarea({
      parent: options.parent,
      top: options.top || 0,
      left: options.left || 0,
      width: options.width || '100%',
      height: options.height || 3,
      border: options.border || { type: 'line' },
      style: {
        fg: 'white',
        bg: 'black',
        border: { fg: 'cyan' },
        focus: { border: { fg: 'yellow' } },
        ...options.style,
      },
      label: options.label,
      hidden: options.hidden,
      inputOnFocus: true,
      scrollable: true,
      keys: true,
      mouse: true,
      tags: false,
    });

    // Ensure style object exists after creation
    if (!this.element.style) {
      this.element.style = {
        fg: 'white',
        bg: 'black',
        border: { fg: 'cyan' },
        focus: { border: { fg: 'yellow' } },
      };
    }

    this.setupEventHandlers();
    this.updatePlaceholder();
  }

  private setupEventHandlers(): void {
    // Basic input handling
    this.element.on('keypress', (ch, key) => {
      this.handleKeyPress(ch, key);
      if (this.options.onKeyPress) {
        this.options.onKeyPress(ch, key);
      }
    });

    // Submit on Enter (unless multiline)
    this.element.key(['enter'], () => {
      if (!this.options.multiline) {
        this.submit();
      } else {
        this.insertText('\n');
      }
    });

    // Ctrl+Enter for submit in multiline mode
    this.element.key(['C-enter'], () => {
      if (this.options.multiline) {
        this.submit();
      }
    });

    // Navigation shortcuts
    this.element.key(['C-a'], () => this.moveToStart());
    this.element.key(['C-e'], () => this.moveToEnd());
    this.element.key(['C-k'], () => this.deleteToEnd());
    this.element.key(['C-u'], () => this.deleteToStart());
    this.element.key(['C-w'], () => this.deleteWord());
    this.element.key(['M-f'], () => this.moveWordForward());
    this.element.key(['M-b'], () => this.moveWordBackward());

    // Copy/Paste
    this.element.key(['C-c'], () => this.copy());
    this.element.key(['C-v'], () => this.paste());
    this.element.key(['C-x'], () => this.cut());

    // History navigation
    this.element.key(['up'], () => this.historyUp());
    this.element.key(['down'], () => this.historyDown());

    // Selection
    this.element.key(['S-left'], () => this.selectLeft());
    this.element.key(['S-right'], () => this.selectRight());
    this.element.key(['C-S-a'], () => this.selectAll());

    // Tab completion
    this.element.key(['tab'], () => {
      this.emit('tab', this.getValue());
    });

    // Value change events
    this.element.on('value', () => {
      if (this.options.onChange) {
        this.options.onChange(this.getValue());
      }
      this.emit('change', this.getValue());
    });

    // Focus events
    this.element.on('focus', () => {
      this.emit('focus');
    });

    this.element.on('blur', () => {
      this.emit('blur');
    });
  }

  private handleKeyPress(ch: string, key: BlessedKeyEvent): void {
    // Handle special characters and key combinations
    if (key.name === 'backspace') {
      this.handleBackspace();
    } else if (key.name === 'delete') {
      this.handleDelete();
    } else if (key.name === 'left') {
      this.moveCursorLeft();
    } else if (key.name === 'right') {
      this.moveCursorRight();
    } else if (ch && ch.length === 1 && !key.ctrl && !key.meta) {
      this.insertText(ch);
    }

    this.updateDisplay();
  }

  private insertText(text: string): void {
    const currentValue = this.getValue();
    const maxLength = this.options.maxLength;

    if (maxLength && currentValue.length + text.length > maxLength) {
      return;
    }

    if (this.hasSelection()) {
      this.deleteSelection();
    }

    const before = currentValue.substring(0, this.cursorPosition);
    const after = currentValue.substring(this.cursorPosition);
    const newValue = before + text + after;

    this.setValue(newValue);
    this.cursorPosition += text.length;
    this.clearSelection();
  }

  private handleBackspace(): void {
    if (this.hasSelection()) {
      this.deleteSelection();
    } else if (this.cursorPosition > 0) {
      const currentValue = this.getValue();
      const before = currentValue.substring(0, this.cursorPosition - 1);
      const after = currentValue.substring(this.cursorPosition);
      this.setValue(before + after);
      this.cursorPosition--;
    }
  }

  private handleDelete(): void {
    if (this.hasSelection()) {
      this.deleteSelection();
    } else {
      const currentValue = this.getValue();
      if (this.cursorPosition < currentValue.length) {
        const before = currentValue.substring(0, this.cursorPosition);
        const after = currentValue.substring(this.cursorPosition + 1);
        this.setValue(before + after);
      }
    }
  }

  private moveCursorLeft(): void {
    if (this.cursorPosition > 0) {
      this.cursorPosition--;
      this.clearSelection();
    }
  }

  private moveCursorRight(): void {
    const currentValue = this.getValue();
    if (this.cursorPosition < currentValue.length) {
      this.cursorPosition++;
      this.clearSelection();
    }
  }

  private moveToStart(): void {
    this.cursorPosition = 0;
    this.clearSelection();
  }

  private moveToEnd(): void {
    this.cursorPosition = this.getValue().length;
    this.clearSelection();
  }

  private deleteToEnd(): void {
    const currentValue = this.getValue();
    const newValue = currentValue.substring(0, this.cursorPosition);
    this.setValue(newValue);
  }

  private deleteToStart(): void {
    const currentValue = this.getValue();
    const newValue = currentValue.substring(this.cursorPosition);
    this.setValue(newValue);
    this.cursorPosition = 0;
  }

  private deleteWord(): void {
    const currentValue = this.getValue();
    const before = currentValue.substring(0, this.cursorPosition);
    const after = currentValue.substring(this.cursorPosition);
    
    // Find word boundary
    const wordMatch = before.match(/\S+\s*$/);
    if (wordMatch) {
      const newBefore = before.substring(0, before.length - wordMatch[0].length);
      this.setValue(newBefore + after);
      this.cursorPosition = newBefore.length;
    }
  }

  private moveWordForward(): void {
    const currentValue = this.getValue();
    const after = currentValue.substring(this.cursorPosition);
    const wordMatch = after.match(/^\s*\S+/);
    
    if (wordMatch) {
      this.cursorPosition += wordMatch[0].length;
    } else {
      this.cursorPosition = currentValue.length;
    }
    this.clearSelection();
  }

  private moveWordBackward(): void {
    const currentValue = this.getValue();
    const before = currentValue.substring(0, this.cursorPosition);
    const wordMatch = before.match(/\S+\s*$/);
    
    if (wordMatch) {
      this.cursorPosition -= wordMatch[0].length;
    } else {
      this.cursorPosition = 0;
    }
    this.clearSelection();
  }

  private hasSelection(): boolean {
    return this.selectionStart !== -1 && this.selectionEnd !== -1;
  }

  private clearSelection(): void {
    this.selectionStart = -1;
    this.selectionEnd = -1;
  }

  private deleteSelection(): void {
    if (!this.hasSelection()) {return;}

    const start = Math.min(this.selectionStart, this.selectionEnd);
    const end = Math.max(this.selectionStart, this.selectionEnd);
    const currentValue = this.getValue();
    
    const before = currentValue.substring(0, start);
    const after = currentValue.substring(end);
    
    this.setValue(before + after);
    this.cursorPosition = start;
    this.clearSelection();
  }

  private selectLeft(): void {
    if (this.selectionStart === -1) {
      this.selectionStart = this.cursorPosition;
    }
    if (this.cursorPosition > 0) {
      this.cursorPosition--;
    }
    this.selectionEnd = this.cursorPosition;
  }

  private selectRight(): void {
    if (this.selectionStart === -1) {
      this.selectionStart = this.cursorPosition;
    }
    const currentValue = this.getValue();
    if (this.cursorPosition < currentValue.length) {
      this.cursorPosition++;
    }
    this.selectionEnd = this.cursorPosition;
  }

  private selectAll(): void {
    this.selectionStart = 0;
    this.selectionEnd = this.getValue().length;
    this.cursorPosition = this.selectionEnd;
  }

  private copy(): void {
    if (this.hasSelection()) {
      const start = Math.min(this.selectionStart, this.selectionEnd);
      const end = Math.max(this.selectionStart, this.selectionEnd);
      this.clipboard = this.getValue().substring(start, end);
    }
  }

  private paste(): void {
    if (this.clipboard) {
      this.insertText(this.clipboard);
    }
  }

  private cut(): void {
    this.copy();
    this.deleteSelection();
  }

  private historyUp(): void {
    if (this.history.length === 0) {return;}
    
    if (this.historyIndex === -1) {
      this.historyIndex = this.history.length - 1;
    } else if (this.historyIndex > 0) {
      this.historyIndex--;
    }
    
    this.setValue(this.history[this.historyIndex]);
    this.moveToEnd();
  }

  private historyDown(): void {
    if (this.historyIndex === -1) {return;}
    
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      this.setValue(this.history[this.historyIndex]);
    } else {
      this.historyIndex = -1;
      this.setValue('');
    }
    this.moveToEnd();
  }

  private submit(): void {
    const value = this.getValue();
    
    // Add to history if not empty and different from last entry
    if (value.trim() && (this.history.length === 0 || this.history[this.history.length - 1] !== value)) {
      this.history.push(value);
      // Limit history size
      if (this.history.length > 100) {
        this.history.shift();
      }
    }
    
    this.historyIndex = -1;
    
    if (this.options.onSubmit) {
      this.options.onSubmit(value);
    }
    
    this.emit('submit', value);
  }

  private updateDisplay(): void {
    this.updatePlaceholder();
    if (this.element.screen) {
      this.element.screen.render();
    }
  }

  private updatePlaceholder(): void {
    // Ensure style object exists before accessing properties
    if (!this.element.style) {
      this.element.style = {};
    }

    try {
      if (this.options.placeholder && !this.getValue()) {
        // Show placeholder text
        this.element.style.fg = 'gray';
      } else {
        this.element.style.fg = this.options.style?.fg || 'white';
      }
    } catch (_error) {
      // Fallback to default styling if setting fails
      if (!this.element.style) {
        this.element.style = {};
      }
      this.element.style.fg = 'white';
    }
  }

  // Public API
  getValue(): string {
    return this.element.getValue();
  }

  setValue(value: string): void {
    this.element.setValue(value);
    this.cursorPosition = Math.min(this.cursorPosition, value.length);
    this.clearSelection();
    this.updateDisplay();
  }

  clear(): void {
    this.setValue('');
    this.cursorPosition = 0;
  }

  focus(): void {
    this.element.focus();
  }

  blur(): void {
    // Type assertion since blessed types don't include blur method
    (this.element as any).blur?.();
  }

  show(): void {
    this.element.show();
    if (this.element.screen) {
      this.element.screen.render();
    }
  }

  hide(): void {
    this.element.hide();
    if (this.element.screen) {
      this.element.screen.render();
    }
  }

  destroy(): void {
    this.element.destroy();
  }

  getElement(): blessed.Widgets.TextareaElement {
    return this.element;
  }

  setPlaceholder(placeholder: string): void {
    this.options.placeholder = placeholder;
    this.updatePlaceholder();
  }

  addToHistory(value: string): void {
    if (value.trim() && (this.history.length === 0 || this.history[this.history.length - 1] !== value)) {
      this.history.push(value);
      if (this.history.length > 100) {
        this.history.shift();
      }
    }
  }

  getHistory(): string[] {
    return [...this.history];
  }

  clearHistory(): void {
    this.history = [];
    this.historyIndex = -1;
  }
}

export default TextInput;
