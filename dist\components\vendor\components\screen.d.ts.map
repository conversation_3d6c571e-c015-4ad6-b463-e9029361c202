{"version": 3, "file": "screen.d.ts", "sourceRoot": "", "sources": ["../../../../src/components/vendor/components/screen.tsx"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAItC,MAAM,WAAW,aAAc,SAAQ,OAAO,CAAC,OAAO,CAAC,cAAc;IACnE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IACnD,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,KAAK,IAAI,CAAC;IAC5E,MAAM,CAAC,EAAE,MAAM,IAAI,CAAC;CACrB;AAED,qBAAa,cAAe,SAAQ,YAAY;IAC9C,OAAO,CAAC,MAAM,CAAyB;IACvC,OAAO,CAAC,OAAO,CAAgB;IAC/B,OAAO,CAAC,WAAW,CAAS;gBAEhB,OAAO,GAAE,aAAkB;IA2BvC;;OAEG;IACH,OAAO,CAAC,kBAAkB;IA0C1B;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAkB9B;;OAEG;IACH,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM;IAInC;;OAEG;IACH,MAAM,IAAI,IAAI;IAMd;;OAEG;IACH,KAAK,IAAI,IAAI;IAKb;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI;IAK1C;;OAEG;IACH,aAAa,IAAI;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAOlD;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAI7B;;OAEG;IACH,OAAO,CAAC,WAAW;IAMnB;;OAEG;IACH,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,KAAK,IAAI,GAAG,IAAI;IAI9G;;OAEG;IACH,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI;IAQzE;;OAEG;IACH,SAAS,IAAI,IAAI;IAIjB;;OAEG;IACH,YAAY,IAAI,IAAI;IAIpB;;OAEG;IACH,UAAU,IAAI,MAAM;IAIpB;;OAEG;IACH,IAAI,CAAC,IAAI,GAAE,MAAU,GAAG,IAAI;IAK5B;;OAEG;IACH,OAAO,IAAI,IAAI;IAef;;OAEG;IACH,IAAI,SAAS,IAAI,OAAO,CAEvB;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,MAAM,CAElB;IAED;;OAEG;IACH,IAAI,MAAM,IAAI,MAAM,CAEnB;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,MAAM,CAElB;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI;IAI3C,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI;IAI3C,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IAI1D,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI;IAInF,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI;CAGnF;AAED,eAAe,cAAc,CAAC"}