/**
 * CLI Tests
 * 
 * Basic tests for the Kritrima AI CLI functionality.
 */

import { describe, it, expect, vi } from 'vitest';
import { CLI_VERSION } from '../src/version.js';

describe('CLI Version', () => {
  it('should have a valid version', () => {
    expect(CLI_VERSION).toBeDefined();
    expect(typeof CLI_VERSION).toBe('string');
    expect(CLI_VERSION).toMatch(/^\d+\.\d+\.\d+/);
  });
});

describe('CLI Basic Functionality', () => {
  it('should export main functions', async () => {
    // Test that main modules can be imported
    const { loadConfig } = await import('../src/utils/config.js');
    expect(typeof loadConfig).toBe('function');
  });

  it('should handle configuration loading', async () => {
    const { loadConfig, validateConfig } = await import('../src/utils/config.js');
    
    // Mock environment to avoid loading actual config
    vi.stubEnv('NODE_ENV', 'test');
    
    const config = loadConfig();
    expect(config).toBeDefined();
    expect(typeof config).toBe('object');
    
    const validation = validateConfig(config);
    expect(validation).toBeDefined();
    expect(Array.isArray(validation.errors)).toBe(true);
  });
});

describe('Provider System', () => {
  it('should have available providers', async () => {
    const { getProviderNames } = await import('../src/utils/providers.js');
    
    const providers = getProviderNames();
    expect(Array.isArray(providers)).toBe(true);
    expect(providers.length).toBeGreaterThan(0);
  });

  it('should validate provider existence', async () => {
    const { hasProvider } = await import('../src/utils/providers.js');
    
    expect(hasProvider('openai')).toBe(true);
    expect(hasProvider('nonexistent-provider')).toBe(false);
  });
});
