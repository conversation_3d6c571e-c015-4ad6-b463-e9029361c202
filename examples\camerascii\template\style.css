/* CameraASCII Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

main {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.video-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.video-section,
.ascii-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-section h3,
.ascii-section h3 {
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.3rem;
}

#videoElement {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 10px;
    display: block;
    margin: 0 auto;
}

.ascii-display {
    background: #000000;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 8px;
    line-height: 1;
    padding: 10px;
    border-radius: 5px;
    overflow: hidden;
    white-space: pre;
    text-align: center;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.controls {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: bold;
    font-size: 0.9rem;
}

.control-group select,
.control-group input[type="range"] {
    padding: 8px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-family: inherit;
}

.control-group select {
    cursor: pointer;
}

.control-group input[type="range"] {
    cursor: pointer;
    height: 30px;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.control-group span {
    font-size: 0.9rem;
    opacity: 0.8;
    text-align: center;
}

.control-group button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-family: inherit;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px 0;
}

.control-group button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.control-group button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.status {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

#statusMessage {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

#performanceInfo {
    font-size: 0.9rem;
    opacity: 0.8;
}

footer {
    text-align: center;
    margin-top: 30px;
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-container {
        grid-template-columns: 1fr;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .controls {
        grid-template-columns: 1fr;
    }
    
    .ascii-display {
        font-size: 6px;
        min-height: 200px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    .ascii-display {
        font-size: 4px;
        min-height: 150px;
    }
}

/* Animation for ASCII output */
.ascii-display.processing {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Error state */
.error {
    background: rgba(255, 0, 0, 0.2);
    border-color: rgba(255, 0, 0, 0.5);
}

.error .ascii-display {
    color: #ff6b6b;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}
