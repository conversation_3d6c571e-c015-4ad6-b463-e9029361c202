/**
 * AI Provider Configuration System
 * 
 * Defines supported AI providers with their configurations,
 * including built-in providers and custom provider support.
 */

import type { ProviderConfig } from '../types/index.js';

/**
 * Built-in AI providers with their default configurations
 */
export const providers: Record<string, ProviderConfig> = {
  openai: {
    name: 'OpenAI',
    baseURL: 'https://api.openai.com/v1',
    envKey: 'OPENAI_API_KEY',
    models: [
      'gpt-4',
      'gpt-4-turbo',
      'gpt-4-turbo-preview',
      'gpt-4-0125-preview',
      'gpt-4-1106-preview',
      'gpt-4-vision-preview',
      'gpt-3.5-turbo',
      'gpt-3.5-turbo-16k',
      'o1-preview',
      'o1-mini',
      'o3-mini',
    ],
    defaultModel: 'gpt-4',
    supportsImages: true,
    supportsTools: true,
    maxContextLength: 128000,
  },

  azure: {
    name: 'Azure OpenAI',
    baseURL: 'https://your-resource.openai.azure.com/openai/deployments/your-deployment',
    envKey: 'AZURE_OPENAI_API_KEY',
    models: [
      'gpt-4',
      'gpt-4-turbo',
      'gpt-35-turbo',
      'gpt-35-turbo-16k',
    ],
    defaultModel: 'gpt-4',
    supportsImages: true,
    supportsTools: true,
    maxContextLength: 128000,
  },

  gemini: {
    name: 'Google Gemini',
    baseURL: 'https://generativelanguage.googleapis.com/v1beta',
    envKey: 'GEMINI_API_KEY',
    models: [
      'gemini-pro',
      'gemini-pro-vision',
      'gemini-1.5-pro',
      'gemini-1.5-flash',
    ],
    defaultModel: 'gemini-pro',
    supportsImages: true,
    supportsTools: true,
    maxContextLength: 1000000,
  },

  ollama: {
    name: 'Ollama',
    baseURL: 'http://localhost:11434/v1',
    envKey: 'OLLAMA_API_KEY',
    models: [
      'llama2',
      'llama2:13b',
      'llama2:70b',
      'codellama',
      'codellama:13b',
      'codellama:34b',
      'mistral',
      'mixtral',
      'neural-chat',
      'starling-lm',
    ],
    defaultModel: 'llama2',
    supportsImages: false,
    supportsTools: true,
    maxContextLength: 4096,
  },

  mistral: {
    name: 'Mistral AI',
    baseURL: 'https://api.mistral.ai/v1',
    envKey: 'MISTRAL_API_KEY',
    models: [
      'mistral-tiny',
      'mistral-small',
      'mistral-medium',
      'mistral-large-latest',
      'mixtral-8x7b-instruct',
      'mixtral-8x22b-instruct',
    ],
    defaultModel: 'mistral-large-latest',
    supportsImages: false,
    supportsTools: true,
    maxContextLength: 32768,
  },

  deepseek: {
    name: 'DeepSeek',
    baseURL: 'https://api.deepseek.com/v1',
    envKey: 'DEEPSEEK_API_KEY',
    models: [
      'deepseek-chat',
      'deepseek-reasoner',
    ],
    defaultModel: 'deepseek-chat',
    supportsImages: false,
    supportsTools: true,
    maxContextLength: 65536, // 64K context length
  },

  xai: {
    name: 'xAI',
    baseURL: 'https://api.x.ai/v1',
    envKey: 'XAI_API_KEY',
    models: [
      'grok-beta',
      'grok-vision-beta',
    ],
    defaultModel: 'grok-beta',
    supportsImages: true,
    supportsTools: true,
    maxContextLength: 131072,
  },

  groq: {
    name: 'Groq',
    baseURL: 'https://api.groq.com/openai/v1',
    envKey: 'GROQ_API_KEY',
    models: [
      'llama2-70b-4096',
      'mixtral-8x7b-32768',
      'gemma-7b-it',
      'llama3-8b-8192',
      'llama3-70b-8192',
    ],
    defaultModel: 'llama3-70b-8192',
    supportsImages: false,
    supportsTools: true,
    maxContextLength: 32768,
  },

  arceeai: {
    name: 'ArceeAI',
    baseURL: 'https://api.arcee.ai/v1',
    envKey: 'ARCEEAI_API_KEY',
    models: [
      'arcee-agent',
      'arcee-nova',
    ],
    defaultModel: 'arcee-agent',
    supportsImages: false,
    supportsTools: true,
    maxContextLength: 8192,
  },

  openrouter: {
    name: 'OpenRouter',
    baseURL: 'https://openrouter.ai/api/v1',
    envKey: 'OPENROUTER_API_KEY',
    models: [
      'openai/gpt-4',
      'openai/gpt-4-turbo',
      'anthropic/claude-3-opus',
      'anthropic/claude-3-sonnet',
      'anthropic/claude-3-haiku',
      'google/gemini-pro',
      'meta-llama/llama-2-70b-chat',
      'mistralai/mixtral-8x7b-instruct',
    ],
    defaultModel: 'openai/gpt-4',
    supportsImages: true,
    supportsTools: true,
    maxContextLength: 128000,
  },
};

/**
 * Get provider configuration by name
 */
export function getProvider(name: string): ProviderConfig | undefined {
  return providers[name.toLowerCase()];
}

/**
 * Get all available provider names
 */
export function getProviderNames(): string[] {
  return Object.keys(providers);
}

/**
 * Check if a provider exists
 */
export function hasProvider(name: string): boolean {
  return name.toLowerCase() in providers;
}

/**
 * Get default model for a provider
 */
export function getDefaultModel(providerName: string): string {
  const provider = getProvider(providerName);
  return provider?.defaultModel || provider?.models?.[0] || 'gpt-4';
}

/**
 * Get available models for a provider
 */
export function getProviderModels(providerName: string): string[] {
  const provider = getProvider(providerName);
  return provider?.models || [];
}

/**
 * Check if provider supports images
 */
export function supportsImages(providerName: string): boolean {
  const provider = getProvider(providerName);
  return provider?.supportsImages || false;
}

/**
 * Check if provider supports tools/function calling
 */
export function supportsTools(providerName: string): boolean {
  const provider = getProvider(providerName);
  return provider?.supportsTools || false;
}

/**
 * Get maximum context length for a provider
 */
export function getMaxContextLength(providerName: string): number {
  const provider = getProvider(providerName);
  return provider?.maxContextLength || 4096;
}

/**
 * Validate provider configuration
 */
export function validateProvider(config: ProviderConfig): string[] {
  const errors: string[] = [];

  if (!config.name) {
    errors.push('Provider name is required');
  }

  if (!config.baseURL) {
    errors.push('Provider base URL is required');
  }

  if (!config.envKey) {
    errors.push('Provider environment key is required');
  }

  if (!config.models || config.models.length === 0) {
    errors.push('Provider must have at least one model');
  }

  if (config.maxContextLength && config.maxContextLength < 1) {
    errors.push('Max context length must be positive');
  }

  return errors;
}

/**
 * Add or update a custom provider
 */
export function addCustomProvider(name: string, config: ProviderConfig): void {
  const errors = validateProvider(config);
  if (errors.length > 0) {
    throw new Error(`Invalid provider configuration: ${errors.join(', ')}`);
  }

  providers[name.toLowerCase()] = config;
}

/**
 * Remove a custom provider
 */
export function removeCustomProvider(name: string): boolean {
  const lowerName = name.toLowerCase();
  if (lowerName in providers) {
    delete providers[lowerName];
    return true;
  }
  return false;
}

/**
 * Get provider configuration with environment overrides
 */
export function getProviderWithOverrides(name: string): ProviderConfig | undefined {
  const provider = getProvider(name);
  if (!provider) {
    return undefined;
  }

  // Apply environment variable overrides
  const overrides: Partial<ProviderConfig> = {};

  const baseUrlEnv = process.env[`${name.toUpperCase()}_BASE_URL`];
  if (baseUrlEnv) {
    overrides.baseURL = baseUrlEnv;
  }

  const apiKeyEnv = process.env[`${name.toUpperCase()}_API_KEY`];
  if (apiKeyEnv) {
    overrides.envKey = `${name.toUpperCase()}_API_KEY`;
  }

  return { ...provider, ...overrides };
}
