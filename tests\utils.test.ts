/**
 * Utility Tests
 * 
 * Tests for utility functions in the Kritrima AI CLI.
 */

import { describe, it, expect } from 'vitest';

describe('Utility Functions', () => {
  it('should handle error utilities', async () => {
    const { toError } = await import('../src/utils/error-utils.js');
    
    const error = toError('test error');
    expect(error).toBeInstanceOf(Error);
    expect(error.message).toBe('test error');
    
    const existingError = new Error('existing');
    const wrappedError = toError(existingError);
    expect(wrappedError).toBe(existingError);
  });

  it('should handle file operations', async () => {
    const { extractFilePaths, validateFileTags } = await import('../src/utils/file-tag-utils.js');

    // Test extracting file paths from text
    const text = 'Check @file1.txt and @src/file2.js';
    const paths = extractFilePaths(text);
    expect(paths).toEqual(['file1.txt', 'src/file2.js']);

    // Test validation (will fail for non-existent files, but function should work)
    const validation = validateFileTags('@nonexistent.txt');
    expect(validation).toBeDefined();
    expect(typeof validation.valid).toBe('boolean');
    expect(Array.isArray(validation.errors)).toBe(true);
  });

  it('should handle terminal utilities', async () => {
    const { getTerminalSize } = await import('../src/utils/terminal.js');
    
    const size = getTerminalSize();
    expect(typeof size.width).toBe('number');
    expect(typeof size.height).toBe('number');
    expect(size.width).toBeGreaterThan(0);
    expect(size.height).toBeGreaterThan(0);
  });
});
