/**
 * Selection Indicator Component
 * 
 * Provides visual indicators for selection states using Unicode symbols
 * with consistent display and accessibility support.
 */

import blessed from 'blessed';
import type { BlessedStyle } from '../../types/blessed-extensions.js';

export interface IndicatorOptions {
  parent: blessed.Widgets.Node;
  top?: string | number;
  left?: string | number;
  width?: string | number;
  height?: string | number;
  type?: 'checkbox' | 'radio' | 'arrow' | 'bullet' | 'custom';
  checked?: boolean;
  disabled?: boolean;
  style?: BlessedStyle;
  customSymbols?: {
    checked: string;
    unchecked: string;
    disabled: string;
  };
  label?: string;
  hidden?: boolean;
}

export const INDICATOR_SYMBOLS = {
  checkbox: {
    checked: '☑',
    unchecked: '☐',
    disabled: '☒',
  },
  radio: {
    checked: '●',
    unchecked: '○',
    disabled: '◌',
  },
  arrow: {
    checked: '▶',
    unchecked: '▷',
    disabled: '▷',
  },
  bullet: {
    checked: '•',
    unchecked: '◦',
    disabled: '◦',
  },
  custom: {
    checked: '✓',
    unchecked: ' ',
    disabled: '✗',
  },
};

export class Indicator {
  private element: blessed.Widgets.BoxElement;
  private options: IndicatorOptions;
  private checked: boolean;
  private disabled: boolean;

  constructor(options: IndicatorOptions) {
    this.options = options;
    this.checked = options.checked || false;
    this.disabled = options.disabled || false;

    this.element = blessed.box({
      parent: options.parent,
      top: options.top || 0,
      left: options.left || 0,
      width: options.width || 'shrink',
      height: options.height || 1,
      style: {
        fg: this.getColor(),
        ...options.style,
      },
      hidden: options.hidden,
      tags: false,
    });

    this.updateDisplay();
  }

  private getSymbols() {
    const type = this.options.type || 'checkbox';
    return this.options.customSymbols || INDICATOR_SYMBOLS[type];
  }

  private getColor(): string {
    if (this.disabled) {
      return 'gray';
    }
    return this.checked ? 'green' : 'white';
  }

  private updateDisplay(): void {
    const symbols = this.getSymbols();
    let symbol: string;

    if (this.disabled) {
      symbol = symbols.disabled;
    } else if (this.checked) {
      symbol = symbols.checked;
    } else {
      symbol = symbols.unchecked;
    }

    let content = symbol;
    if (this.options.label) {
      content += ` ${this.options.label}`;
    }

    this.element.setContent(content);

    if (!this.element.style) {
      this.element.style = {};
    }
    this.element.style.fg = this.getColor();

    if (this.element.screen) {
      this.element.screen.render();
    }
  }

  // Public API
  isChecked(): boolean {
    return this.checked;
  }

  setChecked(checked: boolean): void {
    if (this.disabled) {return;}
    
    this.checked = checked;
    this.updateDisplay();
  }

  toggle(): void {
    this.setChecked(!this.checked);
  }

  isDisabled(): boolean {
    return this.disabled;
  }

  setDisabled(disabled: boolean): void {
    this.disabled = disabled;
    this.updateDisplay();
  }

  setLabel(label: string): void {
    this.options.label = label;
    this.updateDisplay();
  }

  getLabel(): string | undefined {
    return this.options.label;
  }

  setType(type: IndicatorOptions['type']): void {
    this.options.type = type;
    this.updateDisplay();
  }

  getType(): IndicatorOptions['type'] {
    return this.options.type;
  }

  setCustomSymbols(symbols: IndicatorOptions['customSymbols']): void {
    this.options.customSymbols = symbols;
    this.updateDisplay();
  }

  show(): void {
    this.element.show();
    if (this.element.screen) {
      this.element.screen.render();
    }
  }

  hide(): void {
    this.element.hide();
    if (this.element.screen) {
      this.element.screen.render();
    }
  }

  focus(): void {
    this.element.focus();
  }

  blur(): void {
    (this.element as any).blur?.();
  }

  destroy(): void {
    this.element.destroy();
  }

  getElement(): blessed.Widgets.BoxElement {
    return this.element;
  }

  setPosition(top: string | number, left: string | number): void {
    this.element.top = top;
    this.element.left = left;
    if (this.element.screen) {
      this.element.screen.render();
    }
  }

  setSize(width: string | number, height: string | number): void {
    this.element.width = width;
    this.element.height = height;
    if (this.element.screen) {
      this.element.screen.render();
    }
  }
}

/**
 * Create a checkbox indicator
 */
export function createCheckbox(
  parent: blessed.Widgets.Node,
  options: Partial<IndicatorOptions> = {},
): Indicator {
  return new Indicator({
    parent,
    type: 'checkbox',
    ...options,
  });
}

/**
 * Create a radio button indicator
 */
export function createRadio(
  parent: blessed.Widgets.Node,
  options: Partial<IndicatorOptions> = {},
): Indicator {
  return new Indicator({
    parent,
    type: 'radio',
    ...options,
  });
}

/**
 * Create an arrow indicator
 */
export function createArrow(
  parent: blessed.Widgets.Node,
  options: Partial<IndicatorOptions> = {},
): Indicator {
  return new Indicator({
    parent,
    type: 'arrow',
    ...options,
  });
}

/**
 * Create a bullet indicator
 */
export function createBullet(
  parent: blessed.Widgets.Node,
  options: Partial<IndicatorOptions> = {},
): Indicator {
  return new Indicator({
    parent,
    type: 'bullet',
    ...options,
  });
}

/**
 * Create a group of radio indicators that work together
 */
export class RadioGroup {
  private indicators: Indicator[] = [];
  private selectedIndex = -1;

  constructor(
    parent: blessed.Widgets.Node,
    options: Array<{ label: string; value?: string; disabled?: boolean }>,
    groupOptions: Partial<IndicatorOptions> = {},
  ) {
    options.forEach((option, index) => {
      const indicator = new Indicator({
        parent,
        type: 'radio',
        label: option.label,
        disabled: option.disabled,
        top: (groupOptions.top as number || 0) + index,
        left: groupOptions.left,
        width: groupOptions.width,
        ...groupOptions,
      });

      // Add click handler
      indicator.getElement().on('click', () => {
        this.select(index);
      });

      this.indicators.push(indicator);
    });
  }

  select(index: number): void {
    if (index < 0 || index >= this.indicators.length) {return;}
    if (this.indicators[index].isDisabled()) {return;}

    // Uncheck all others
    this.indicators.forEach((indicator, i) => {
      indicator.setChecked(i === index);
    });

    this.selectedIndex = index;
  }

  getSelected(): number {
    return this.selectedIndex;
  }

  getSelectedIndicator(): Indicator | null {
    return this.selectedIndex >= 0 ? this.indicators[this.selectedIndex] : null;
  }

  setDisabled(index: number, disabled: boolean): void {
    if (index >= 0 && index < this.indicators.length) {
      this.indicators[index].setDisabled(disabled);
    }
  }

  destroy(): void {
    this.indicators.forEach(indicator => indicator.destroy());
    this.indicators = [];
  }

  getIndicators(): Indicator[] {
    return [...this.indicators];
  }
}

/**
 * Create a group of checkbox indicators
 */
export class CheckboxGroup {
  private indicators: Indicator[] = [];

  constructor(
    parent: blessed.Widgets.Node,
    options: Array<{ label: string; value?: string; checked?: boolean; disabled?: boolean }>,
    groupOptions: Partial<IndicatorOptions> = {},
  ) {
    options.forEach((option, index) => {
      const indicator = new Indicator({
        parent,
        type: 'checkbox',
        label: option.label,
        checked: option.checked,
        disabled: option.disabled,
        top: (groupOptions.top as number || 0) + index,
        left: groupOptions.left,
        width: groupOptions.width,
        ...groupOptions,
      });

      // Add click handler
      indicator.getElement().on('click', () => {
        if (!indicator.isDisabled()) {
          indicator.toggle();
        }
      });

      this.indicators.push(indicator);
    });
  }

  getChecked(): number[] {
    return this.indicators
      .map((indicator, index) => ({ indicator, index }))
      .filter(({ indicator }) => indicator.isChecked())
      .map(({ index }) => index);
  }

  getCheckedIndicators(): Indicator[] {
    return this.indicators.filter(indicator => indicator.isChecked());
  }

  setChecked(index: number, checked: boolean): void {
    if (index >= 0 && index < this.indicators.length) {
      this.indicators[index].setChecked(checked);
    }
  }

  setDisabled(index: number, disabled: boolean): void {
    if (index >= 0 && index < this.indicators.length) {
      this.indicators[index].setDisabled(disabled);
    }
  }

  checkAll(): void {
    this.indicators.forEach(indicator => {
      if (!indicator.isDisabled()) {
        indicator.setChecked(true);
      }
    });
  }

  uncheckAll(): void {
    this.indicators.forEach(indicator => indicator.setChecked(false));
  }

  destroy(): void {
    this.indicators.forEach(indicator => indicator.destroy());
    this.indicators = [];
  }

  getIndicators(): Indicator[] {
    return [...this.indicators];
  }
}

export default Indicator;
