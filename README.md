# Kritrima AI CLI

[![npm version](https://badge.fury.io/js/kritrima.svg)](https://badge.fury.io/js/kritrima)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D22.0.0-brightgreen.svg)](https://nodejs.org/)

A comprehensive AI coding assistant with autonomous agent capabilities, designed to enhance your development workflow across multiple platforms.

## 🚀 Features

- **Multi-Provider AI Support**: OpenAI, Anthropic, and more
- **Autonomous Agent Capabilities**: Intelligent code analysis and suggestions
- **Cross-Platform Compatibility**: Windows 11, WSL, Linux, and macOS
- **Interactive CLI Interface**: Rich terminal UI with real-time feedback
- **Flexible Approval Modes**: From suggestions to full automation
- **Git Integration**: Smart repository awareness
- **Extensible Architecture**: Plugin-ready design

## 📦 Installation

> **⚠️ IMPORTANT**: The package name is `kritrima` (not `kritrima-ai`)

### Global Installation (Recommended)

```bash
npm install -g kritrima
```

### ⚠️ **Common Installation Issue Fix**

If you're getting Sharp-related errors, make sure you're installing the correct package:

```bash
# ❌ WRONG - Don't use this
npm install -g kritrima-ai

# ✅ CORRECT - Use this
npm install -g kritrima
```

### Local Installation

```bash
npm install kritrima
```

### Platform-Specific Notes

#### Windows 11
```powershell
# Using PowerShell
npm install -g kritrima
```

#### WSL (Windows Subsystem for Linux)
```bash
# Inside WSL environment
npm install -g kritrima
```

#### Linux
```bash
# Ubuntu/Debian
sudo npm install -g kritrima

# Or using yarn
yarn global add kritrima
```

#### macOS
```bash
# Using npm
npm install -g kritrima

# Using Homebrew (if available)
brew install node
npm install -g kritrima
```

## 🛠️ Requirements

- **Node.js**: Version 22.0.0 or higher
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **Git**: For repository integration (optional but recommended)

## 🚀 Quick Start

1. **Install the CLI**:
   ```bash
   npm install -g kritrima
   ```

2. **Run the interactive setup**:
   ```bash
   kritrima-ai
   ```
   The CLI will automatically detect that you need setup and launch the onboarding wizard!

3. **Or use the dedicated setup command**:
   ```bash
   kritrima-ai setup
   ```

4. **Start using the CLI**:
   ```bash
   kritrima-ai "Help me refactor this function"
   ```

### 🎯 First-Time Setup

When you run `kritrima-ai` for the first time, you'll be guided through:

- **Provider Selection**: Choose from OpenAI, Anthropic, Google Gemini, Ollama, and more
- **API Key Configuration**: Step-by-step instructions for each provider
- **Model Selection**: Pick the best model for your needs
- **Preferences Setup**: Configure approval modes, notifications, and debug settings
- **Validation**: Test your configuration before you start coding

The entire setup takes less than 2 minutes! 🚀

## 📖 Usage

### Basic Commands

```bash
# Interactive mode
kritrima-ai

# Direct prompt
kritrima-ai "Explain this code"

# Single-pass mode
kritrima-ai --single-pass "Generate a React component"

# Full-context mode
kritrima-ai --full-context "Analyze the entire project"
```

### Setup and Configuration

```bash
# Interactive setup wizard
kritrima-ai setup

# Configuration wizard
kritrima-ai config --wizard

# Quick setup for specific provider
kritrima-ai setup --provider openai

# List current configuration
kritrima-ai config --list

# Set configuration values
kritrima-ai config --set model=gpt-4
kritrima-ai config --set temperature=0.7

# Reset to defaults
kritrima-ai config --reset
```

### Provider and Model Management

```bash
# List available providers
kritrima-ai providers

# List models for a provider
kritrima-ai models openai

# Get detailed provider information
kritrima-ai providers --verbose
```

### Diagnostic Tools

```bash
# Run system diagnostics
kritrima-ai doctor

# Check for updates
kritrima-ai update
```

## ⚙️ Configuration

### Configuration File

The CLI uses a configuration file located at:
- **Windows**: `%APPDATA%\kritrima-ai\config.json`
- **Linux/macOS**: `~/.config/kritrima-ai/config.json`

### Environment Variables

```bash
# API Keys
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"

# Configuration overrides
export KRITRIMA_MODEL="gpt-4"
export KRITRIMA_PROVIDER="openai"
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `provider` | string | `openai` | AI provider to use |
| `model` | string | `gpt-4` | AI model to use |
| `approvalMode` | string | `suggest` | Approval mode: suggest, auto-edit, full-auto |
| `temperature` | number | `0.7` | AI response temperature (0-2) |
| `maxTokens` | number | `4000` | Maximum tokens per response |
| `timeout` | number | `30000` | Request timeout in milliseconds |
| `debug` | boolean | `false` | Enable debug logging |
| `notifications` | boolean | `true` | Enable desktop notifications |
| `saveHistory` | boolean | `true` | Save command history |

## 🔧 Advanced Usage

### Approval Modes

- **suggest**: AI provides suggestions, user approves each action
- **auto-edit**: AI automatically edits files with user oversight
- **full-auto**: AI operates autonomously with minimal user intervention

### Working with Projects

```bash
# Set working directory
kritrima-ai --workdir /path/to/project

# Use custom config file
kritrima-ai --config /path/to/config.json

# Enable full context analysis
kritrima-ai --full-context "Optimize the entire codebase"
```

## 🧪 Development

### Building from Source

```bash
# Clone the repository
git clone https://github.com/kritrima/kritrima.git
cd kritrima

# Install dependencies
npm install

# Build the project
npm run build

# Run tests
npm test

# Run in development mode
npm run dev
```

### Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint

# Type checking
npm run typecheck
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Run tests: `npm test`
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [GitHub Wiki](https://github.com/kritrima/kritrima/wiki)
- **Issues**: [GitHub Issues](https://github.com/kritrima/kritrima/issues)
- **Discussions**: [GitHub Discussions](https://github.com/kritrima/kritrima/discussions)

## 🔄 Changelog

See [CHANGELOG.md](CHANGELOG.md) for a detailed history of changes.

## 🙏 Acknowledgments

- OpenAI for their powerful AI models
- The Node.js and TypeScript communities
- All contributors and users of this project

---

Made with ❤️ by the Kritrima AI team
