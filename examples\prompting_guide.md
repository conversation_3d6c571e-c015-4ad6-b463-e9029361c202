# Kritrima AI CLI - Comprehensive Prompting Guide

This guide provides best practices, techniques, and examples for effective prompting with the Kritrima AI CLI to maximize productivity and achieve better results.

## Table of Contents

1. [Basic Prompting Principles](#basic-prompting-principles)
2. [Effective Prompting Strategies](#effective-prompting-strategies)
3. [Code-Specific Prompting](#code-specific-prompting)
4. [Advanced Techniques](#advanced-techniques)
5. [Common Patterns](#common-patterns)
6. [Troubleshooting](#troubleshooting)
7. [Examples](#examples)

## Basic Prompting Principles

### 1. Be Specific and Clear
- **Good**: "Create a TypeScript function that validates email addresses using regex and returns a boolean"
- **Bad**: "Make an email validator"

### 2. Provide Context
- Include relevant file paths with `@filename.ext`
- Mention the programming language and framework
- Describe the project structure when relevant

### 3. Break Down Complex Tasks
- **Good**: "First, create the interface for user data, then implement the validation logic, finally add error handling"
- **Bad**: "Build a complete user management system"

### 4. Use Action-Oriented Language
- Start with verbs: "Create", "Implement", "Fix", "Optimize", "Refactor"
- Be explicit about what you want: "Add", "Remove", "Update", "Replace"

## Effective Prompting Strategies

### 1. Incremental Development
```
1. "Create a basic Express.js server with a health check endpoint"
2. "Add middleware for logging and error handling"
3. "Implement user authentication with JWT"
4. "Add rate limiting and security headers"
```

### 2. Context Building
```
"I'm working on a React TypeScript project using Vite. 
The project structure is:
- src/components/ (React components)
- src/hooks/ (custom hooks)
- src/utils/ (utility functions)
- src/types/ (TypeScript interfaces)

Please create a custom hook for managing form state with validation."
```

### 3. Error-Driven Development
```
"I'm getting this error: [paste error message]
The error occurs in @src/components/UserForm.tsx
Please analyze the issue and provide a fix."
```

## Code-Specific Prompting

### 1. File References
Use `@` to reference files:
```
"Review @src/utils/api.ts and @src/types/user.ts, then create a new function to fetch user profiles"
```

### 2. Code Modifications
```
"In @src/components/Button.tsx:
1. Add a 'variant' prop with options: 'primary', 'secondary', 'danger'
2. Update the styling to use CSS modules
3. Add proper TypeScript types"
```

### 3. Testing Requests
```
"Create comprehensive unit tests for @src/utils/validation.ts using Jest and TypeScript. 
Include edge cases and error scenarios."
```

### 4. Documentation
```
"Add JSDoc comments to all functions in @src/utils/database.ts and create a README.md 
explaining the database utility functions."
```

## Advanced Techniques

### 1. Multi-Step Workflows
```
"I need to implement user authentication:
1. Create TypeScript interfaces for User and AuthState
2. Implement JWT token handling utilities
3. Create React context for auth state management
4. Add login/logout components
5. Implement protected route wrapper
6. Add error handling and loading states"
```

### 2. Architecture Decisions
```
"I'm building a real-time chat application with React and Node.js.
Please suggest and implement:
1. The optimal project structure
2. State management approach (Context vs Redux)
3. WebSocket integration strategy
4. Database schema design
5. Error handling patterns"
```

### 3. Performance Optimization
```
"Analyze @src/components/DataTable.tsx for performance issues.
The component renders slowly with large datasets (1000+ rows).
Please optimize for:
1. Virtual scrolling
2. Memoization
3. Efficient re-renders
4. Memory usage"
```

### 4. Code Review and Refactoring
```
"Review the code quality in @src/services/ directory.
Please identify and fix:
1. Code duplication
2. Performance bottlenecks
3. Type safety issues
4. Error handling gaps
5. Suggest architectural improvements"
```

## Common Patterns

### 1. Feature Implementation
```
"Implement a [feature name] that:
- Does [specific functionality]
- Integrates with [existing system/API]
- Follows [specific pattern/architecture]
- Includes [testing/documentation requirements]"
```

### 2. Bug Fixing
```
"There's a bug in @[file path]:
- Expected behavior: [description]
- Actual behavior: [description]
- Steps to reproduce: [steps]
- Error messages: [if any]
Please investigate and fix."
```

### 3. Code Generation
```
"Generate [type of code] for [specific use case]:
- Input: [data structure/parameters]
- Output: [expected result]
- Constraints: [limitations/requirements]
- Style: [coding standards/patterns]"
```

### 4. Integration Tasks
```
"Integrate [service/library] with the existing codebase:
- Current architecture: [description]
- Integration points: [where to connect]
- Configuration needed: [settings/environment]
- Testing strategy: [how to verify]"
```

## Troubleshooting

### 1. When AI Doesn't Understand
- Provide more context about your project
- Include relevant file contents with `@filename`
- Break down the request into smaller steps
- Use more specific technical terminology

### 2. When Code Doesn't Work
- Share the complete error message
- Include the relevant file paths
- Describe the expected vs actual behavior
- Mention your environment (Node.js version, OS, etc.)

### 3. When Results Are Too Generic
- Specify your exact tech stack
- Mention coding standards or patterns you follow
- Include examples of your preferred code style
- Reference existing code files for consistency

### 4. When You Need Explanations
- Ask for step-by-step explanations
- Request comments in the generated code
- Ask for alternative approaches
- Request pros and cons of different solutions

## Examples

### Example 1: Creating a New Feature
```
"I need to add a user profile editing feature to my React TypeScript app.

Current structure:
- @src/types/User.ts (user interface)
- @src/components/UserProfile.tsx (display component)
- @src/hooks/useUser.ts (user data hook)

Please:
1. Create an editable user profile form component
2. Add form validation using react-hook-form
3. Implement save/cancel functionality
4. Add loading and error states
5. Update the existing UserProfile component to toggle between view/edit modes"
```

### Example 2: Debugging an Issue
```
"I'm getting a TypeScript error in @src/utils/api.ts:

Error: "Property 'data' does not exist on type 'AxiosResponse<unknown>'"

The function is supposed to fetch user data from our API.
Please review the code and fix the type issues while maintaining type safety."
```

### Example 3: Performance Optimization
```
"The @src/components/ProductList.tsx component is slow when rendering 500+ products.

Current implementation uses:
- useState for product data
- useEffect for API calls
- map() to render product cards

Please optimize for:
1. Virtual scrolling or pagination
2. Memoization where appropriate
3. Efficient state updates
4. Lazy loading of images"
```

### Example 4: Testing Implementation
```
"Create comprehensive tests for @src/utils/formatters.ts:

The file contains functions for:
- formatCurrency(amount, locale)
- formatDate(date, format)
- formatPhoneNumber(phone, country)

Please create Jest tests with:
1. Happy path scenarios
2. Edge cases (null, undefined, invalid inputs)
3. Different locales and formats
4. Performance tests for large datasets"
```

## Best Practices Summary

1. **Start Small**: Begin with simple, focused requests
2. **Build Context**: Use file references and provide project details
3. **Be Iterative**: Make incremental improvements
4. **Test Early**: Ask for tests alongside implementation
5. **Document Intent**: Explain the "why" behind your requests
6. **Review Results**: Always review and test generated code
7. **Learn Patterns**: Notice what prompting styles work best for your projects

## Slash Commands Quick Reference

- `/help` - Show available commands
- `/model` - Switch AI model or provider
- `/history` - View command history
- `/clear` - Clear conversation
- `/compact` - Compress conversation context
- `/diff` - Show git diff
- `/bug` - Generate bug report
- `/approval` - Change approval mode

Remember: The AI is most effective when you provide clear context, specific requirements, and iterative feedback. Don't hesitate to ask for clarifications or alternative approaches!
