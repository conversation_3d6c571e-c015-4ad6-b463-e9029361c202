name: "CameraASCII"
description: "Webcam feed to ASCII art conversion"
technology: ["HTML5", "JavaScript", "Canvas API", "WebRTC"]
difficulty: "intermediate"
estimated_time: "2-3 hours"

prompt: |
  Create a real-time webcam to ASCII art converter web application with the following requirements:

  ## Core Features:
  1. **Webcam Access**: Use getUserMedia() to access the user's webcam
  2. **Real-time Processing**: Convert video frames to ASCII art in real-time
  3. **ASCII Conversion**: Map pixel brightness to ASCII characters
  4. **Adjustable Settings**: Allow users to adjust:
     - ASCII character set (simple to complex)
     - Resolution/detail level
     - Contrast and brightness
     - Frame rate
  5. **Display Options**: Show both original video and ASCII output side by side

  ## Technical Requirements:
  - Pure HTML5, CSS3, and JavaScript (no external libraries)
  - Responsive design that works on desktop and mobile
  - Smooth performance (aim for 15-30 FPS)
  - Error handling for camera access issues
  - Clean, modern UI with controls

  ## ASCII Character Sets:
  - Simple: " .:-=+*#%@"
  - Medium: " .`^\",:;Il!i~+_-?][}{1)(|\\/tfjrxnuvczXYUJCLQ0OZmwqpdbkhao*#MW&8%B@$"
  - Complex: Full ASCII printable character set

  ## Implementation Details:
  1. Create an HTML page with video element and canvas for ASCII output
  2. Implement camera initialization with proper error handling
  3. Create ASCII conversion algorithm:
     - Sample video frames at regular intervals
     - Convert to grayscale
     - Map brightness values to ASCII characters
     - Render to canvas or pre-formatted text element
  4. Add UI controls for real-time adjustment
  5. Implement performance optimizations (downsampling, efficient rendering)

  ## File Structure:
  ```
  index.html          # Main HTML page
  style.css           # Styling and layout
  script.js           # Main JavaScript logic
  ascii-converter.js  # ASCII conversion algorithms
  camera-handler.js   # Camera access and video handling
  ui-controls.js      # User interface controls
  README.md           # Documentation and usage instructions
  ```

  ## Bonus Features (if time permits):
  - Save ASCII frames as text files
  - Record ASCII video sequences
  - Different ASCII art styles (block characters, etc.)
  - Color ASCII art option
  - Fullscreen mode
  - Social sharing capabilities

  Please start by creating the basic HTML structure and camera access functionality, then progressively add the ASCII conversion and UI controls.

metadata:
  tags: ["webcam", "ascii-art", "real-time", "canvas", "webrtc"]
  learning_objectives:
    - "WebRTC and getUserMedia API usage"
    - "Canvas manipulation and pixel processing"
    - "Real-time video processing techniques"
    - "Performance optimization for browser applications"
    - "ASCII art generation algorithms"
