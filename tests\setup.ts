/**
 * Vitest Test Setup
 * 
 * Global test configuration and setup for the Kritrima AI CLI project.
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';

// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.VITEST = 'true';
  
  // Disable notifications during tests
  process.env.KRITRIMA_NOTIFICATIONS = 'false';
  
  // Use test configuration
  process.env.KRITRIMA_CONFIG_DIR = './tests/fixtures';
});

afterAll(async () => {
  // Cleanup after all tests
});

beforeEach(async () => {
  // Setup before each test
});

afterEach(async () => {
  // Cleanup after each test
});
