{"name": "kritrima", "version": "1.4.4", "description": "Comprehensive AI coding assistant with autonomous agent capabilities and interactive onboarding", "main": "dist/cli.js", "bin": {"kritrima-ai": "./bin/kritrima-ai.js", "kritrima": "./bin/kritrima-ai.js"}, "type": "module", "scripts": {"build": "npm run clean && tsc && npm run fix-imports && npm run copy-assets", "fix-imports": "node scripts/fix-imports.js", "copy-assets": "node -e \"const fs=require('fs'),path=require('path'); if(fs.existsSync('src/assets')){fs.cpSync('src/assets','dist/assets',{recursive:true})}\"", "dev": "tsx src/cli.tsx", "start": "node dist/cli.js", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint src/**/*.ts src/**/*.tsx", "lint:fix": "eslint src/**/*.ts src/**/*.tsx --fix", "typecheck": "tsc --noEmit", "clean": "node -e \"const fs=require('fs'); if(fs.existsSync('dist')){fs.rmSync('dist',{recursive:true,force:true})}\"", "prepare": "npm run build", "prepublishOnly": "npm run clean && npm run build && npm run test && npm run lint && npm run typecheck", "prepack": "npm run build", "postinstall": "node -e \"console.log('\\n🚀 Kritrima AI CLI installed successfully!\\n📖 Run \\'kritrima-ai --help\\' to get started.\\n')\"", "build:watch": "tsc --watch", "dev:debug": "tsx --inspect src/cli.tsx", "publish:dry-run": "npm publish --dry-run", "publish:prod": "npm publish --access public"}, "keywords": ["ai", "cli", "coding-assistant", "autonomous-agent", "openai", "anthropic", "typescript", "terminal", "development-tools", "code-analysis", "automation", "cross-platform", "windows", "linux", "macos", "wsl"], "author": {"name": "Kritrima AI", "email": "<EMAIL>", "url": "https://kritrima.ai"}, "license": "MIT", "files": ["dist/**/*", "bin/**/*", "README.md", "LICENSE", "CHANGELOG.md"], "os": ["win32", "darwin", "linux"], "cpu": ["x64", "arm64"], "engines": {"node": ">=22.0.0"}, "dependencies": {"blessed": "^0.1.81", "chalk": "^5.3.0", "chokidar": "^4.0.1", "commander": "^12.1.0", "date-fns": "^4.1.0", "debug": "^4.4.0", "dotenv": "^16.4.7", "execa": "^9.5.1", "fs-extra": "^11.2.0", "glob": "^11.0.0", "https-proxy-agent": "^7.0.5", "inquirer": "^12.1.0", "lodash": "^4.17.21", "mime-types": "^2.1.35", "nanoid": "^5.0.9", "node-notifier": "^10.0.1", "openai": "^4.67.3", "ora": "^8.1.1", "semver": "^7.6.3", "yaml": "^2.6.1", "zod": "^3.23.8"}, "optionalDependencies": {"sharp": "^0.33.5"}, "devDependencies": {"@types/blessed": "^0.1.25", "@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.13", "@types/mime-types": "^2.1.4", "@types/node": "^22.10.2", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitest/coverage-v8": "^2.1.8", "eslint": "^9.17.0", "tsx": "^4.19.2", "typescript": "^5.8.0", "typescript-eslint": "^8.33.0", "vitest": "^2.1.8"}, "repository": {"type": "git", "url": "https://github.com/kritrima/kritrima.git"}, "bugs": {"url": "https://github.com/kritrima/kritrima/issues"}, "homepage": "https://github.com/kritrima/kritrima#readme"}