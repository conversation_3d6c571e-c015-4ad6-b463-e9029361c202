/**
 * Advanced Terminal Chat Input Component
 *
 * Sophisticated input system with multi-line editing, auto-completion,
 * file tag expansion, slash commands, and history management.
 */
import blessed from 'blessed';
import MultilineEditor from './multiline-editor.js';
import { expandFileTags, getFileSuggestions } from '../../utils/file-tag-utils.js';
import { getSlashCommandSuggestions, parseSlashCommand, isSlashCommand, } from '../../utils/slash-commands.js';
import { logError } from '../../utils/logger/log.js';
export class TerminalChatInput {
    container;
    editor;
    statusBar;
    autoCompleteList;
    options;
    history = [];
    historyIndex = -1;
    autoCompleteItems = [];
    autoCompleteVisible = false;
    autoCompleteIndex = 0;
    currentHeight = 3;
    workingDirectory;
    constructor(options) {
        this.options = options;
        this.workingDirectory = options.workingDirectory || process.cwd();
        // Create main container
        this.container = blessed.box({
            parent: options.parent,
            top: options.top || 0,
            left: options.left || 0,
            width: options.width || '100%',
            height: options.height || 3,
            border: { type: 'line' },
            style: {
                border: { fg: 'cyan' },
            },
        });
        // Ensure container style object exists
        if (!this.container.style) {
            this.container.style = {
                border: { fg: 'cyan' },
            };
        }
        // Create multiline editor
        this.editor = new MultilineEditor({
            parent: this.container,
            top: 0,
            left: 0,
            width: '100%',
            height: '100%-1',
            placeholder: options.placeholder || 'Type your message... (Ctrl+D to send, Ctrl+C to cancel)',
            onSubmit: (text) => this.handleSubmit(text),
            onCancel: () => this.handleCancel(),
            onChange: (text) => this.handleTextChange(text),
            onCursorMove: () => this.updateAutoComplete(),
        });
        // Create status bar
        this.statusBar = blessed.box({
            parent: this.container,
            bottom: 0,
            left: 0,
            width: '100%',
            height: 1,
            style: {
                fg: 'gray',
                bg: 'black',
            },
            content: 'Ctrl+D: Send | Ctrl+C: Cancel | Tab: Auto-complete',
        });
        // Ensure status bar style object exists
        if (!this.statusBar.style) {
            this.statusBar.style = {
                fg: 'gray',
                bg: 'black',
            };
        }
        // Create auto-complete list
        this.autoCompleteList = blessed.list({
            parent: this.container.screen,
            top: 0,
            left: 0,
            width: 40,
            height: 8,
            border: { type: 'line' },
            style: {
                border: { fg: 'yellow' },
                selected: { bg: 'blue' },
            },
            hidden: true,
            mouse: true,
            keys: true,
            vi: false,
        });
        // Ensure auto-complete list style object exists
        if (!this.autoCompleteList.style) {
            this.autoCompleteList.style = {
                border: { fg: 'yellow' },
                selected: { bg: 'blue' },
            };
        }
        this.setupEventHandlers();
        this.updateHeight();
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Tab completion
        this.editor.getElement().key(['tab'], () => {
            this.handleTabCompletion();
        });
        // History navigation
        this.editor.getElement().key(['up'], (_ch, key) => {
            if (key.ctrl) {
                this.navigateHistory(-1);
            }
        });
        this.editor.getElement().key(['down'], (_ch, key) => {
            if (key.ctrl) {
                this.navigateHistory(1);
            }
        });
        // Auto-complete navigation
        this.editor.getElement().key(['C-n'], () => {
            if (this.autoCompleteVisible) {
                this.navigateAutoComplete(1);
            }
        });
        this.editor.getElement().key(['C-p'], () => {
            if (this.autoCompleteVisible) {
                this.navigateAutoComplete(-1);
            }
        });
        // Accept auto-complete
        this.editor.getElement().key(['enter'], () => {
            if (this.autoCompleteVisible) {
                this.acceptAutoComplete();
                return false; // Prevent default enter handling
            }
            return true; // Allow default enter handling
        });
        // Escape to hide auto-complete
        this.editor.getElement().key(['escape'], () => {
            this.hideAutoComplete();
        });
        // Auto-complete list events
        this.autoCompleteList.on('select', (_item, index) => {
            this.autoCompleteIndex = index || 0;
            this.acceptAutoComplete();
        });
        // Dynamic height adjustment
        this.editor.getElement().on('keypress', () => {
            setTimeout(() => this.updateHeight(), 0);
        });
    }
    /**
     * Handle text submission
     */
    async handleSubmit(text) {
        const trimmedText = text.trim();
        if (!trimmedText) {
            return;
        }
        // Add to history
        this.addToHistory(trimmedText);
        // Check for slash commands
        if (isSlashCommand(trimmedText)) {
            const { command, parameters } = parseSlashCommand(trimmedText);
            if (command && this.options.onSlashCommand) {
                this.options.onSlashCommand(command.command, parameters);
                this.clear();
                return;
            }
        }
        // Expand file tags
        try {
            const expandedText = await expandFileTags(trimmedText, this.workingDirectory);
            if (this.options.onSubmit) {
                this.options.onSubmit(expandedText);
            }
            this.clear();
        }
        catch (error) {
            logError('Failed to expand file tags', error);
            // Submit original text if expansion fails
            if (this.options.onSubmit) {
                this.options.onSubmit(trimmedText);
            }
            this.clear();
        }
    }
    /**
     * Handle cancellation
     */
    handleCancel() {
        this.clear();
        if (this.options.onCancel) {
            this.options.onCancel();
        }
    }
    /**
     * Handle text change
     */
    handleTextChange(_text) {
        this.updateAutoComplete();
        this.updateHeight();
    }
    /**
     * Handle tab completion
     */
    handleTabCompletion() {
        if (this.autoCompleteVisible && this.autoCompleteItems.length > 0) {
            this.acceptAutoComplete();
        }
        else {
            this.showAutoComplete();
        }
    }
    /**
     * Update auto-complete suggestions
     */
    updateAutoComplete() {
        const text = this.editor.getText();
        const cursorPos = this.editor.getCursorPosition();
        const currentLine = text.split('\n')[cursorPos.row] || '';
        const beforeCursor = currentLine.slice(0, cursorPos.col);
        this.autoCompleteItems = [];
        // File tag completion
        if (beforeCursor.includes('@')) {
            const fileSuggestions = getFileSuggestions(beforeCursor, this.workingDirectory);
            this.autoCompleteItems.push(...fileSuggestions.map(file => ({
                text: file,
                description: 'File',
                type: 'file',
                insertText: file,
            })));
        }
        // Slash command completion
        if (beforeCursor.startsWith('/')) {
            const commandSuggestions = getSlashCommandSuggestions(beforeCursor);
            this.autoCompleteItems.push(...commandSuggestions.map(cmd => ({
                text: cmd.command,
                description: cmd.description,
                type: 'command',
                insertText: cmd.command,
            })));
        }
        // History completion
        if (text.length > 2) {
            const historyMatches = this.history
                .filter(item => item.toLowerCase().includes(text.toLowerCase()))
                .slice(0, 5);
            this.autoCompleteItems.push(...historyMatches.map(item => ({
                text: item,
                description: 'History',
                type: 'history',
                insertText: item,
            })));
        }
        // Update auto-complete display
        if (this.autoCompleteItems.length > 0 && this.autoCompleteVisible) {
            this.updateAutoCompleteDisplay();
        }
        else if (this.autoCompleteItems.length === 0) {
            this.hideAutoComplete();
        }
    }
    /**
     * Show auto-complete list
     */
    showAutoComplete() {
        if (this.autoCompleteItems.length === 0) {
            this.updateAutoComplete();
        }
        if (this.autoCompleteItems.length === 0) {
            return;
        }
        this.autoCompleteVisible = true;
        this.autoCompleteIndex = 0;
        this.updateAutoCompleteDisplay();
        this.positionAutoComplete();
        this.autoCompleteList.show();
        if (this.container.screen) {
            this.container.screen.render();
        }
    }
    /**
     * Hide auto-complete list
     */
    hideAutoComplete() {
        this.autoCompleteVisible = false;
        this.autoCompleteList.hide();
        if (this.container.screen) {
            this.container.screen.render();
        }
    }
    /**
     * Update auto-complete display
     */
    updateAutoCompleteDisplay() {
        const items = this.autoCompleteItems.map(item => {
            const typeIcon = item.type === 'file' ? '📁' :
                item.type === 'command' ? '⚡' : '🕒';
            return `${typeIcon} ${item.text}${item.description ? ` - ${item.description}` : ''}`;
        });
        this.autoCompleteList.setItems(items);
        this.autoCompleteList.select(this.autoCompleteIndex);
    }
    /**
     * Position auto-complete list
     */
    positionAutoComplete() {
        const containerPos = Number(this.container.atop) || 0;
        const containerLeft = Number(this.container.aleft) || 0;
        const cursorPos = this.editor.getCursorPosition();
        this.autoCompleteList.top = containerPos + cursorPos.row + 1;
        this.autoCompleteList.left = containerLeft + cursorPos.col;
    }
    /**
     * Navigate auto-complete list
     */
    navigateAutoComplete(direction) {
        if (!this.autoCompleteVisible || this.autoCompleteItems.length === 0) {
            return;
        }
        this.autoCompleteIndex = Math.max(0, Math.min(this.autoCompleteItems.length - 1, this.autoCompleteIndex + direction));
        this.autoCompleteList.select(this.autoCompleteIndex);
        if (this.container.screen) {
            this.container.screen.render();
        }
    }
    /**
     * Accept current auto-complete suggestion
     */
    acceptAutoComplete() {
        if (!this.autoCompleteVisible || this.autoCompleteItems.length === 0) {
            return;
        }
        const selectedItem = this.autoCompleteItems[this.autoCompleteIndex];
        if (!selectedItem) {
            return;
        }
        // Insert the completion
        const text = this.editor.getText();
        const cursorPos = this.editor.getCursorPosition();
        const lines = text.split('\n');
        const currentLine = lines[cursorPos.row] || '';
        // Find the completion context
        let insertPos = cursorPos.col;
        if (selectedItem.type === 'file' && currentLine.includes('@')) {
            const atIndex = currentLine.lastIndexOf('@', cursorPos.col);
            if (atIndex !== -1) {
                insertPos = atIndex + 1;
            }
        }
        else if (selectedItem.type === 'command' && currentLine.startsWith('/')) {
            insertPos = 1;
        }
        // Replace the current word with the completion
        const beforeCompletion = currentLine.slice(0, insertPos);
        const afterCompletion = currentLine.slice(cursorPos.col);
        const newLine = beforeCompletion + selectedItem.insertText + afterCompletion;
        lines[cursorPos.row] = newLine;
        this.editor.setText(lines.join('\n'));
        // Move cursor to end of completion
        this.editor.getElement().focus();
        this.hideAutoComplete();
    }
    /**
     * Navigate command history
     */
    navigateHistory(direction) {
        if (this.history.length === 0) {
            return;
        }
        this.historyIndex = Math.max(-1, Math.min(this.history.length - 1, this.historyIndex + direction));
        if (this.historyIndex === -1) {
            this.editor.clear();
        }
        else {
            this.editor.setText(this.history[this.historyIndex]);
        }
    }
    /**
     * Add text to history
     */
    addToHistory(text) {
        // Remove duplicates
        const index = this.history.indexOf(text);
        if (index !== -1) {
            this.history.splice(index, 1);
        }
        // Add to beginning
        this.history.unshift(text);
        // Limit history size
        if (this.history.length > 100) {
            this.history = this.history.slice(0, 100);
        }
        this.historyIndex = -1;
    }
    /**
     * Update input height based on content
     */
    updateHeight() {
        const lineCount = this.editor.getLineCount();
        const maxHeight = this.options.maxHeight || 10;
        const newHeight = Math.min(Math.max(lineCount + 2, 3), maxHeight);
        if (newHeight !== this.currentHeight) {
            this.currentHeight = newHeight;
            this.container.height = newHeight;
            if (this.options.onHeightChange) {
                this.options.onHeightChange(newHeight);
            }
            if (this.container.screen) {
                this.container.screen.render();
            }
        }
    }
    /**
     * Get current text
     */
    getText() {
        return this.editor.getText();
    }
    /**
     * Set text content
     */
    setText(text) {
        this.editor.setText(text);
    }
    /**
     * Clear input
     */
    clear() {
        this.editor.clear();
        this.hideAutoComplete();
        this.historyIndex = -1;
    }
    /**
     * Focus the input
     */
    focus() {
        this.editor.focus();
    }
    /**
     * Check if input is empty
     */
    isEmpty() {
        return this.editor.isEmpty();
    }
    /**
     * Set working directory
     */
    setWorkingDirectory(directory) {
        this.workingDirectory = directory;
    }
    /**
     * Get the container element
     */
    getElement() {
        return this.container;
    }
    /**
     * Show the input component
     */
    show() {
        this.container.show();
        if (this.container.screen) {
            this.container.screen.render();
        }
    }
    /**
     * Hide the input component
     */
    hide() {
        this.container.hide();
        this.hideAutoComplete();
        if (this.container.screen) {
            this.container.screen.render();
        }
    }
    /**
     * Destroy the input component
     */
    destroy() {
        this.hideAutoComplete();
        this.autoCompleteList.destroy();
        this.editor.destroy();
        this.container.destroy();
    }
}
export default TerminalChatInput;
//# sourceMappingURL=terminal-chat-input.js.map